## 需求1
主要实现android设备调试工具方面的内容。

## adb要求使用airtest 1.3.5的版本内置的adb，不要使用其他的adb。

## 功能点1:设备连接（使用airtest+poco实现）
1. 支持自动扫描当前所连接的所有android设备，初始状态为未连接，点击连接按钮后，会自动连接设备，连接成功后，状态变为已连接。
2. 支持切换设备，点击切换设备按钮后，会弹出设备列表，选择设备后，会自动连接设备，连接成功后，状态变为已连接。
3. 支持断开设备连接，点击断开设备连接按钮后，会断开设备连接，状态变为未连接。
4. 需要设计一个交互优秀的界面，方便用户进行设备连接。
5. 选择一个设备连接后就持久化当前的airtest的device属性和poco属性，后续的操作直接使用，不需要在重新实例化。
6. 通过airtest的device属性，获取当前的设备信息，包括设备名称、设备型号、设备版本、设备分辨率、设备屏幕大小等。

## 功能点2:安装包功能（（使用airtest+poco实现）
1. 前端界面需要设计一个安装包url输入框，用户可以输入安装包的url，点击安装按钮后，会自动下载安装包（如果下载的到本地包名称一样则覆盖下载），并安装到设备上。
2. 安装包下载进度需要实时显示，安装进度需要实时显示。
3. 需要有一个安装包列表，可以查看已经安装的包，可以删除已经安装的包。
4. 下载包的过程可以在后台进行，前端需要实时显示下载进度。并且支持多个任务排队下载。

## 功能点3:业务操作抽象出来，方便后续可以替换底层实现，例如android点击操作，底层可以使用airtest+poco，后续也可以直接替换底层实现为appium，对应的业务操作方法调用不变。
1. 获取dom树
2. 截图
3. 点击
4. 输入
5. 滑动
6. 断言

## 功能点4:业务操作对应的底层实现（使用airtest+poco实现）
1. 通过poco获取页面dom树，并且遍历这个dom树进行标记索引，index拼接每一层+seq_index的序号，seq_index为每个元素的序号
2. 通过airtest进行截图，并且提供用户选择是否标记元素，如果标记元素，则根据标号后的dom树的payload的pos、size属性进行框选，框选需要随机使用不同的颜色，并且将seq_index标记在每个框选的左上角；如果用户不标记元素，则直接截图。
3. 设置一个全局变量，存储这个标记元素的截图和处理后的dom树，在下次没有重新获取dom树的时候，直接使用这个全局变量，不需要重新获取dom树。
4. 前端页面需要有截图操作，并且截图后需要在界面中展示出来，支持点击放大和缩小。
5. 前端页面需要有获取dom树操作（默认需要遍历dom树，并且标记索引），并且需要将dom树展示出来，支持查看每个元素节点的payload属性，并且支持模糊搜索dom树，支持通过seq_index进行搜索。
6. 考虑截图和dom的前端展示是否可以放在一起，并且支持点击那个元素框选自动查找到这个元素信息。
7. 点击操作通过poco实现，并且支持通过坐标点击、seq_index索引点击（给这个索引，需要通过dom查询出对应的元素坐标进行点击）、支持输入poco语句进行定位元素去点击。
8. 输入操作通过poco实现，并且支持通过坐标输入、seq_index索引输入（给这个索引，需要通过dom查询出对应的元素坐标进行输入）、支持输入poco语句进行定位元素去输入。
9. 滑动操作通过poco实现，默认支持向上下滑动0.3、支持输入poco语句进行定位元素去滑动。
10. 断言操作通过poco实现，断言输入的poco语句元素是否存在。

## 功能点5:adb命令调试输入框
1. 使用内置的airtest的adb，前端界面有一个输入框，支持输入adb shell命令，并且支持执行adb shell命令，并且支持将执行结果展示出来。
2. 支持adb shell命令的输入历史记录，并且支持点击历史记录进行执行。

## 功能点6:poco语句调试输入框
1. 使用内置的airtest的poco，前端界面有一个输入框，支持输入poco语句，并且支持执行poco语句，并且支持将执行结果展示出来。
2. 支持poco语句的输入历史记录，并且支持点击历史记录进行执行。

## 功能点7:通过adb获取设备日志
1. 通过adb获取设备日志，并且支持将日志展示出来。（日志内容很多需要好的交互设计）

## 功能点8:所有的操作需要有日志记录，并且支持查看日志。可以看到实时的日志记录。


## 修改意见（下面都是已经确定实现的内容了）
1. 服务端涉及到的接口路径需要有/api/android/开头，涉及到日志等公共的接口需要有/api/common/开头。
2. 截图期望可以查看截图目录里面所有的截图，并且有时间信息，支持删除指定截图和删除所有截图
3. adb命令调试输入框期望可以固定好adb shell，用户输入后面的命令就行了
4. 期望在页面操作里面新增一个模块，期望可以通过minicamp或者别的方式可以实时展示当前设备的页面内容，并且可以直接对这个页面进行点击、滑动等一系列操作
5. 通过seq_index点击提示找不到这个元素，是不是查找seq_index的方法有问题
6. 业务操作页面期望可以滚动，不然元素详情页面会超出屏幕，无法查看。其他页面也有类似的问题
7. 获取设备日志有问题，报错，另外adb命令输入的地方期望可以固定adb shell，用户输入后面的命令就行了，给的实例也不对，不是ps｜xxxx，是用户使用的adb命令例如 adb -s emulator-5556 shell getprop ro.build.version.sdk，我只需要输入shell getprop ro.build.version.sdk部分即可
8. 帮我检查一下坐标点击好像不太准确
9. 如果点击的是截图并且标记元素，那么同时页展示刚刚获取的dom树信息
10. 操作面板新增一个设置项，如果设置了那么每次操作完后自动执行截图并且标记元素
11. 设备信息期望可以使用表格展示，现在展示的太丑了
12. 创建了一个下载任务，但是前端的下载任务模块没有内容，执行好像报错了2025-06-10 21:55:45 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'install -r packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk' 失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
2025-06-10 21:55:45 | ERROR    | services.package_service:_install_apk:143 - 安装APK失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
13. 优化一下操作面板的展示，现在展示的太丑了
14. 截图管理必须要手动点击一下刷新才有数据，不然进来看没有数据
15. 所有页面不要设置最大高度，动态根据页面内容可以进行滑动
16. 操作面板的截图按钮开关没有提示内容，只有一个开关
17. 期望可以输入一个云端的android设备ip，可以进行连接
18. 日志的文件大小还是Invalid Date，需要优化一下
19. 点击报错了，点击操作失败: [object Object]{
    "message": "Request failed with status code 422",
    "name": "AxiosError",
    "stack": "AxiosError: Request failed with status code 422\n    at settle (http://localhost:3000/node_modules/.vite/deps/axios.js?v=cd925164:1229:12)\n    at XMLHttpRequest.onloadend (http://localhost:3000/node_modules/.vite/deps/axios.js?v=cd925164:1561:7)",
    "config": {
        "transitional": {
            "silentJSONParsing": true,
            "forcedJSONParsing": true,
            "clarifyTimeoutError": false
        },
        "adapter": [
            "xhr",
            "http",
            "fetch"
        ],
        "transformRequest": [
            null
        ],
        "transformResponse": [
            null
        ],
        "timeout": 30000,
        "xsrfCookieName": "XSRF-TOKEN",
        "xsrfHeaderName": "X-XSRF-TOKEN",
        "maxContentLength": -1,
        "maxBodyLength": -1,
        "env": {},
        "headers": {
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        },
        "baseURL": "",
        "method": "post",
        "url": "/api/android/operation/click",
        "data": "{\"method\":\"seq_index\",\"x\":305,\"y\":486,\"seq_index\":164,\"poco_query\":\"\"}",
        "allowAbsoluteUrls": true
    },
    "code": "ERR_BAD_REQUEST",
    "status": 422
}
20. dom树期望双击可以展开
21. poco语句调试期望放到操作面板里面，并且期望输入的poco格式为 poco(xxxxx).xxx.xxx.xxx
22. 将设备日志模块放到右侧
23. 设备截图模块期望固定窗口大小，图片在里面进行缩放，并且所有的页面还是没有办法滚动，改多少次了还是改不好
24. 操作后等待5s自动截图
25. 所有页面都可以滚动，并且是响应式的，动态调整布局


26. 操作面板有两个poco调试，重复了
27. 期望可以选择截图的元素框自动获取到对应的元素信息
28. 输入操作选中元素坐标没有自动回填进去
29. Poco语句执行完成，但可能有错误,执行结果里面期望也有日志展示，执行报错了执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
30. 设备日志，获取设备日志失败: Cannot read properties of undefined (reading 'logs')

31. 将整体布局都重新设计一下，并且将左侧菜单放到顶部
32. 所有页面都不要设置固定、最大高度，完全是动态可以滑动的

33. 对于业务操作页面的修改意见
    - 这个页面期望水平最多两个模块
    - 对于元素详情期望放在操作面板模块的下面
    - 操作面板里面的设计样式不太好看，输入框也太大了，重新设计一下这个模块的样式
    - 操作面板里面有重复的poco调试组件，只留一个，并且期望的poco调试输入案例为：poco(xxxx).xxx.xxx.xxx，用户需要写入poco语句，而不是(xxxx).xxx.xxx.xxx()，执行的结果和日志都需要展示
34. 对于调试工具页面的修改意见
    - 去掉poco语句调试这个重复的模块
    - 将AD命令调试模块放到放到业务操作页面的设备屏幕和DOM树模块下面
    - 将设备日志模块放到放到业务操作页面的元素详情期模块下面
    - 去掉调试工具页面

35. 顶部的菜不要把所有二级菜单展示出来，只展示一级菜单，一级菜单有android调试工具、日志管理，剩下的都是android调试工具内的二级菜单
36. 每个模块卡片固定大小，里面可以进行滚动，这个模块卡片的大小你工具功能进行设计一下，并且同一垂直的卡片宽度要一样
    - 业务操作页面的几个模块对于用户使用场景是：先去截个图片，然后看一下对应的dom元素，然后选择一个元素查看具体的信息，然后在去操作面板进行操作，现在这样的模块设计导致用户使用起来非常不方便，期望可以重新设计一下
37. 设备屏幕和DOM树模块的截图展示太大了，期望固定这个容器，在这个容器内展示图片可以进行滚动，可以缩放

38. 设备管理页面设计优化
    - 期望可用设备模块和设备信息模块并排
    - 左侧都是可用设备列表（包括云端设备连接），右侧是设备信息模块