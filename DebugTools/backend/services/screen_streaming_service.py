"""
Android设备屏幕实时流媒体服务
支持实时屏幕展示、录制视频、截图等功能
"""

import asyncio
import subprocess
import threading
import time
import base64
import json
import os
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ScreenStreamingService:
    def __init__(self):
        self.device_id: Optional[str] = None
        self.is_streaming = False
        self.is_recording = False
        self.stream_process: Optional[subprocess.Popen] = None
        self.record_process: Optional[subprocess.Popen] = None
        self.websocket_clients: List = []
        self.stream_thread: Optional[threading.Thread] = None
        self.fps = 10  # 默认10fps，可调节
        self.quality = 80  # JPEG质量 1-100
        self.scale = 0.5  # 缩放比例，减少带宽
        
        # 录制相关
        self.recording_dir = "recordings"
        self.screenshots_dir = "screenshots/streaming"
        
        # 确保目录存在
        os.makedirs(self.recording_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    def set_device(self, device_id: str):
        """设置目标设备"""
        self.device_id = device_id
        logger.info(f"设置流媒体目标设备: {device_id}")
    
    def add_websocket_client(self, websocket):
        """添加WebSocket客户端"""
        self.websocket_clients.append(websocket)
        logger.info(f"添加WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    def remove_websocket_client(self, websocket):
        """移除WebSocket客户端"""
        if websocket in self.websocket_clients:
            self.websocket_clients.remove(websocket)
        logger.info(f"移除WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    async def broadcast_to_clients(self, message: Dict[str, Any]):
        """向所有WebSocket客户端广播消息"""
        if not self.websocket_clients:
            return
        
        message_str = json.dumps(message)
        disconnected_clients = []
        
        for client in self.websocket_clients:
            try:
                await client.send_text(message_str)
            except Exception as e:
                logger.warning(f"向客户端发送消息失败: {e}")
                disconnected_clients.append(client)
        
        # 移除断开连接的客户端
        for client in disconnected_clients:
            self.remove_websocket_client(client)
    
    def start_streaming(self, fps: int = 10, quality: int = 80, scale: float = 0.5) -> bool:
        """开始屏幕流媒体"""
        if not self.device_id:
            logger.error("未设置设备ID")
            return False
        
        if self.is_streaming:
            logger.warning("流媒体已在运行")
            return True
        
        self.fps = fps
        self.quality = quality
        self.scale = scale
        
        try:
            # 启动流媒体线程
            self.is_streaming = True
            self.stream_thread = threading.Thread(target=self._stream_worker, daemon=True)
            self.stream_thread.start()
            
            logger.info(f"开始屏幕流媒体，FPS: {fps}, 质量: {quality}, 缩放: {scale}")
            return True
        except Exception as e:
            logger.error(f"启动流媒体失败: {e}")
            self.is_streaming = False
            return False
    
    def stop_streaming(self):
        """停止屏幕流媒体"""
        self.is_streaming = False
        
        if self.stream_process:
            try:
                self.stream_process.terminate()
                self.stream_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.stream_process.kill()
            except Exception as e:
                logger.warning(f"停止流媒体进程失败: {e}")
            finally:
                self.stream_process = None
        
        if self.stream_thread:
            self.stream_thread.join(timeout=5)
            self.stream_thread = None
        
        logger.info("屏幕流媒体已停止")
    
    def _stream_worker(self):
        """流媒体工作线程"""
        frame_interval = 1.0 / self.fps
        
        while self.is_streaming:
            try:
                start_time = time.time()
                
                # 使用adb screencap获取屏幕截图
                cmd = [
                    'adb', '-s', self.device_id, 'exec-out', 
                    'screencap', '-p'
                ]
                
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    timeout=2
                )
                
                if result.returncode == 0 and result.stdout:
                    # 将PNG数据转换为base64
                    image_data = base64.b64encode(result.stdout).decode('utf-8')
                    
                    # 发送到WebSocket客户端
                    message = {
                        'type': 'frame',
                        'data': image_data,
                        'timestamp': time.time(),
                        'format': 'png'
                    }
                    
                    # 使用asyncio在主线程中发送
                    asyncio.run_coroutine_threadsafe(
                        self.broadcast_to_clients(message),
                        asyncio.get_event_loop()
                    )
                
                # 控制帧率
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
            except subprocess.TimeoutExpired:
                logger.warning("屏幕截图超时")
            except Exception as e:
                logger.error(f"流媒体工作线程错误: {e}")
                time.sleep(0.1)  # 避免错误循环
    
    def start_recording(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """开始录制视频"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}
        
        if self.is_recording:
            return {"success": False, "message": "录制已在进行中"}
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screen_record_{timestamp}.mp4"
        
        filepath = os.path.join(self.recording_dir, filename)
        
        try:
            # 使用adb screenrecord录制视频
            cmd = [
                'adb', '-s', self.device_id, 'shell', 
                'screenrecord', '--verbose',
                f'/sdcard/{filename}'
            ]
            
            self.record_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.is_recording = True
            self.current_recording_file = filename
            
            logger.info(f"开始录制视频: {filename}")
            return {
                "success": True, 
                "message": "录制已开始",
                "filename": filename,
                "filepath": filepath
            }
            
        except Exception as e:
            logger.error(f"开始录制失败: {e}")
            return {"success": False, "message": f"录制失败: {str(e)}"}
    
    def stop_recording(self) -> Dict[str, Any]:
        """停止录制视频"""
        if not self.is_recording:
            return {"success": False, "message": "当前没有录制任务"}
        
        try:
            if self.record_process:
                # 发送Ctrl+C停止录制
                self.record_process.terminate()
                self.record_process.wait(timeout=10)
            
            # 从设备拉取录制的文件
            local_path = os.path.join(self.recording_dir, self.current_recording_file)
            device_path = f"/sdcard/{self.current_recording_file}"
            
            pull_cmd = ['adb', '-s', self.device_id, 'pull', device_path, local_path]
            result = subprocess.run(pull_cmd, capture_output=True, timeout=30)
            
            if result.returncode == 0:
                # 删除设备上的文件
                rm_cmd = ['adb', '-s', self.device_id, 'shell', 'rm', device_path]
                subprocess.run(rm_cmd, timeout=5)
                
                self.is_recording = False
                self.record_process = None
                
                logger.info(f"录制完成: {self.current_recording_file}")
                return {
                    "success": True,
                    "message": "录制完成",
                    "filename": self.current_recording_file,
                    "filepath": local_path
                }
            else:
                raise Exception("拉取录制文件失败")
                
        except Exception as e:
            logger.error(f"停止录制失败: {e}")
            self.is_recording = False
            self.record_process = None
            return {"success": False, "message": f"停止录制失败: {str(e)}"}
    
    def take_screenshot(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """截图当前屏幕"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"streaming_screenshot_{timestamp}.png"
        
        filepath = os.path.join(self.screenshots_dir, filename)
        
        try:
            # 使用adb screencap截图
            cmd = ['adb', '-s', self.device_id, 'exec-out', 'screencap', '-p']
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            
            if result.returncode == 0 and result.stdout:
                with open(filepath, 'wb') as f:
                    f.write(result.stdout)
                
                logger.info(f"截图完成: {filename}")
                return {
                    "success": True,
                    "message": "截图完成",
                    "filename": filename,
                    "filepath": filepath
                }
            else:
                raise Exception("截图命令执行失败")
                
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return {"success": False, "message": f"截图失败: {str(e)}"}
    
    def get_status(self) -> Dict[str, Any]:
        """获取流媒体状态"""
        return {
            "device_id": self.device_id,
            "is_streaming": self.is_streaming,
            "is_recording": self.is_recording,
            "connected_clients": len(self.websocket_clients),
            "fps": self.fps,
            "quality": self.quality,
            "scale": self.scale
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop_streaming()
        if self.is_recording:
            self.stop_recording()
        self.websocket_clients.clear()

# 全局实例
screen_streaming_service = ScreenStreamingService()
