2025-06-10 21:33:14 | ERROR    | api.operation_router:get_dom_tree:174 - 获取DOM树失败: 
2025-06-10 21:33:16 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:33:16 | ERROR    | api.device_router:get_device_info:87 - 获取设备信息失败: 
2025-06-10 21:33:16 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vdzttd5j3_1749562287985 断开连接
2025-06-10 21:33:17 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:33:17 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:33:20 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:33:20 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:33:22 | INFO     | services.airtest_poco_service:get_dom_tree:282 - 正在获取DOM树...
2025-06-10 21:33:23 | INFO     | services.airtest_poco_service:get_dom_tree:301 - DOM树获取成功，共 170 个元素
2025-06-10 21:33:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:33:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:33:44 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:33:44 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:35:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:35:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:35:43 | ERROR    | api.operation_router:take_screenshot:65 - 截图失败: 
Traceback (most recent call last):
  File "/Users/<USER>/code/DebugTools/backend/api/operation_router.py", line 51, in take_screenshot
    raise HTTPException(status_code=400, detail="没有连接的设备")
fastapi.exceptions.HTTPException

2025-06-10 21:36:08 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:36:08 | ERROR    | api.device_router:get_device_info:87 - 获取设备信息失败: 
2025-06-10 21:36:09 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4ah5nn6ms_1749562396940 断开连接
2025-06-10 21:36:09 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:36:30 | INFO     | api.operation_router:delete_screenshot:132 - 删除截图文件: screenshots/1749560956144.jpg
2025-06-10 21:36:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_lofxmfleb_1749562569345 断开连接
2025-06-10 21:36:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:37:14 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:37:14 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:37:17 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:37:18 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:37:20 | INFO     | services.airtest_poco_service:get_dom_tree:282 - 正在获取DOM树...
2025-06-10 21:37:21 | INFO     | services.airtest_poco_service:get_dom_tree:307 - DOM树获取成功，共 170 个元素
2025-06-10 21:37:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_lofxmfleb_1749562569345 断开连接
2025-06-10 21:37:52 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:37:54 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:38:13 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:38:15 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:38:16 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:38:19 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:38:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:38:42 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_uisss32by_1749562673148 断开连接
2025-06-10 21:38:44 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:38:48 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:38:48 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 170 个元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_213940.png
2025-06-10 21:39:45 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_213940.png
2025-06-10 21:40:03 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0, 0) 成功
2025-06-10 21:40:17 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:40:17 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 201 个元素
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214016.png
2025-06-10 21:40:18 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214016.png
2025-06-10 21:40:23 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (418, 998) 成功
2025-06-10 21:40:28 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (407, 402) 成功
2025-06-10 21:40:32 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (248, 413) 成功
2025-06-10 21:40:42 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:40:42 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214041.png
2025-06-10 21:40:43 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214041.png
2025-06-10 21:40:50 | ERROR    | services.airtest_poco_service:click_by_seq_index:458 - 未找到seq_index为 57 的元素
2025-06-10 21:41:39 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:41:43 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:41:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214142.png
2025-06-10 21:41:44 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214142.png
2025-06-10 21:41:51 | ERROR    | services.airtest_poco_service:click_by_seq_index:458 - 未找到seq_index为 89 的元素
2025-06-10 21:41:56 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (524, 995) 成功
2025-06-10 21:41:59 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:00 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:03 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (553, 974) 成功
2025-06-10 21:42:06 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (358, 989) 成功
2025-06-10 21:42:10 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (465, 716) 成功
2025-06-10 21:42:15 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:42:16 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 203 个元素
2025-06-10 21:43:07 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:07 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:07 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:09 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:09 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:09 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:13 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:13 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-10 21:43:13 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-10 21:43:19 | ERROR    | services.airtest_poco_service:execute_adb_command:622 - 执行ADB命令 'ls' 失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:43:19 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:43:19 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: ls
2025-06-10 21:43:22 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 21:43:22 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5s9cmi6k_1749562722512 断开连接
2025-06-10 21:43:28 | ERROR    | api.debug_router:get_device_logs:162 - 获取设备日志失败: 
2025-06-10 21:46:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5s9cmi6k_1749562722512 断开连接
2025-06-10 21:46:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_m882f57bi_1749563179165 断开连接
2025-06-10 21:46:58 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_qcnd6pgaj_1749563185780 断开连接
2025-06-10 21:47:00 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_iwtdgdr86_1749563219163 断开连接
2025-06-10 21:47:02 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kivj36sh8_1749563220670 断开连接
2025-06-10 21:47:48 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_s8cshca6t_1749563223019 断开连接
2025-06-10 21:47:49 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wce4qbx3j_1749563268771 断开连接
2025-06-10 21:48:00 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_xp44pelz5_1749563270112 断开连接
2025-06-10 21:48:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_97xyuv0yr_1749563280380 断开连接
2025-06-10 21:48:09 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:48:28 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:48:31 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:48:31 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 21:48:39 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:48:39 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_214834.png
2025-06-10 21:48:40 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_214834.png
2025-06-10 21:48:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:48:43 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:50:05 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 173 个元素
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215019.png
2025-06-10 21:50:20 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215019.png
2025-06-10 21:50:27 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.5, 0.5047008547008547) 成功
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 45 个元素
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:50:32 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:50:33 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215031.png
2025-06-10 21:50:33 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215031.png
2025-06-10 21:50:37 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (503, 785) 成功
2025-06-10 21:50:47 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.8212962962962963, 0.5901709401709402) 成功
2025-06-10 21:51:25 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:51:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 114 个元素
2025-06-10 21:51:29 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:51:29 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 115 个元素
2025-06-10 21:52:14 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 21:52:14 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 114 个元素
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_215213.png
2025-06-10 21:52:15 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_215213.png
2025-06-10 21:53:05 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.287962962962963, 0.3782051282051282) 成功
2025-06-10 21:54:06 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:55:12 | INFO     | services.package_service:start_download_and_install:52 - 创建下载安装任务: acf18cfa-1bf5-4d36-95eb-e1dea96c7a6d, URL: http://download2.ctripcorp.com/mcd/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 21:55:45 | INFO     | services.package_service:_download_file:115 - 文件下载完成: packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 21:55:45 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'install -r packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk' 失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
2025-06-10 21:55:45 | ERROR    | services.package_service:_install_apk:143 - 安装APK失败: stdout[b''] stderr[b'/system/bin/sh: install: not found\n']
2025-06-10 21:56:31 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:57:13 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_7qw1qxquf_1749563289156 断开连接
2025-06-10 21:58:10 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_magxdht5f_1749563833327 断开连接
2025-06-10 21:58:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_pbnwnmwvx_1749563890254 断开连接
2025-06-10 21:58:38 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'ls' 失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:58:38 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b'acct\nbin\nbt_firmware\nbugreports\ncache\ncharger\nconfig\ncust\nd\ndata\ndefault.prop\ndev\ndsp\netc\nfirmware\nlost+found\nmnt\nodm\noem\nproc\nproduct\nres\nsbin\nsdcard\nstorage\nsys\nsystem\nvendor\n'] stderr[b'ls: ./init.zygote32.rc: Permission denied\nls: ./init.recovery.qcom.rc: Permission denied\nls: ./ueventd.rc: Permission denied\nls: ./init.miui.early_boot.sh: Permission denied\nls: ./init.miui.post_boot.sh: Permission denied\nls: ./init.usb.configfs.rc: Permission denied\nls: ./init.recovery.hardware.rc: Permission denied\nls: ./init.miui.google_revenue_share.rc: Permission denied\nls: ./init.mishow.ctl.rc: Permission denied\nls: ./init.batteryd.rc: Permission denied\nls: ./init.miui.cust.rc: Permission denied\nls: ./persist: Permission denied\nls: ./init.rc: Permission denied\nls: ./init.usb.rc: Permission denied\nls: ./init.zygote64_32.rc: Permission denied\nls: ./init.miui.rc: Permission denied\nls: ./init.environ.rc: Permission denied\nls: ./init.miui.nativedebug.rc: Permission denied\nls: ./init: Permission denied\nls: ./verity_key: Permission denied\nls: ./init.miui.google_revenue_share_v2.rc: Permission denied\n']
2025-06-10 21:58:38 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: ls
2025-06-10 21:59:10 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:44 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:48 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:48 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_mexfxlbt4_1749563911267 断开连接
2025-06-10 21:59:48 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 21:59:50 | INFO     | services.airtest_poco_service:disconnect_device:73 - 设备连接已断开
2025-06-10 21:59:50 | INFO     | services.device_service:disconnect_device:111 - 设备连接已断开
2025-06-10 21:59:51 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 21:59:52 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 21:59:52 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:00:05 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:00:57 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:01:22 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:01:22 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220118.png
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220118.png
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:01:23 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:03:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:03:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:31 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:03:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_0c8rkzkfr_1749563988699 断开连接
2025-06-10 22:03:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_cq9p7b5ob_1749561909654 断开连接
2025-06-10 22:04:12 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_3u1ky2nc3_1749564234158 断开连接
2025-06-10 22:04:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vvdp3s1gn_1749564252527 断开连接
2025-06-10 22:06:25 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:06:26 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:06:29 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:06:29 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:06:40 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:06:40 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 3 个元素
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220637.png
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220637.png
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 3 个元素
2025-06-10 22:06:47 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:06:47 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 198 个元素
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220646.png
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220646.png
2025-06-10 22:06:48 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:06:49 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:06:55 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0, 0) 成功
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:07:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220702.png
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220702.png
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:05 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 197 个元素
2025-06-10 22:07:09 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (537, 1154) 成功
2025-06-10 22:07:56 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:07:56 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 254 个元素
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:07:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220755.png
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220755.png
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:07:58 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 254 个元素
2025-06-10 22:08:02 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (328, 516) 成功
2025-06-10 22:08:03 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:03 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 255 个元素
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220802.png
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220802.png
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 255 个元素
2025-06-10 22:08:18 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (0.5648148148148148, 0.36495726495726494) 成功
2025-06-10 22:08:19 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:19 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 80 个元素
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220818.png
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220818.png
2025-06-10 22:08:20 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:24 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 249 个元素
2025-06-10 22:08:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_j99ftz922_1749564254698 断开连接
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 248 个元素
2025-06-10 22:08:56 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_220855.png
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_220855.png
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:08:57 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 248 个元素
2025-06-10 22:09:22 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:09:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:26 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:26 | ERROR    | api.device_router:get_device_info:91 - 获取设备信息失败: 
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:10:38 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:38 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_l32p9fsg9_1749564533790 断开连接
2025-06-10 22:10:40 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:10:41 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:10:44 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:10:45 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_5hayq4gua_1749564640875 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:23 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:11:54 | ERROR    | api.operation_router:take_screenshot:65 - 截图失败: 
Traceback (most recent call last):
  File "/Users/<USER>/code/DebugTools/backend/api/operation_router.py", line 51, in take_screenshot
    raise HTTPException(status_code=400, detail="没有连接的设备")
fastapi.exceptions.HTTPException

2025-06-10 22:11:57 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:11:57 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-10 22:11:57 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_5hayq4gua_1749564640875 断开连接
2025-06-10 22:11:58 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:11:59 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:12:02 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:12:02 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:13:22 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_06zrjhlmx_1749564717932 断开连接
2025-06-10 22:13:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:13:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:13:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:13:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_a5akz3mmt_1749564234160 断开连接
2025-06-10 22:13:25 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:13:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_yiyusthsr_1749564805184 断开连接
2025-06-10 22:13:50 | INFO     | services.package_service:start_download_and_install:52 - 创建下载安装任务: 48ad66d6-7be4-484c-8f8c-61a6ee16f122, URL: http://download2.ctripcorp.com/mcd/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 22:13:50 | INFO     | services.package_service:_download_file:90 - 删除已存在的文件: packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 22:14:05 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_q1au24e31_1749564802448 断开连接
2025-06-10 22:14:08 | INFO     | services.package_service:_download_file:115 - 文件下载完成: packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 22:14:37 | INFO     | services.package_service:_install_apk:143 - APK安装成功: packages/IBU_App_V8.28.2_jdzdhb_Debug_FAT_28272892.apk
2025-06-10 22:14:37 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_6r84t9j5h_1749564845671 断开连接
2025-06-10 22:14:51 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:14:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_lurw1wp1z_1749564891702 断开连接
2025-06-10 22:14:53 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:14:55 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_7agecceic_1749564893514 断开连接
2025-06-10 22:14:55 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:14:56 | INFO     | services.airtest_poco_service:disconnect_device:73 - 设备连接已断开
2025-06-10 22:14:56 | INFO     | services.device_service:disconnect_device:111 - 设备连接已断开
2025-06-10 22:14:56 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-10 22:14:57 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-10 22:14:58 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-10 22:15:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_5xoh8kn7d_1749564895123 断开连接
2025-06-10 22:15:37 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vd2io1x2t_1749564933206 断开连接
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 141 个元素
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_221626.png
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_221626.png
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:16:31 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 141 个元素
2025-06-10 22:17:48 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:17:48 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:17:48 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 141 个元素
2025-06-10 22:17:48 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:17:48 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:17:49 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_221747.png
2025-06-10 22:17:49 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_221747.png
2025-06-10 22:17:49 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:17:49 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 141 个元素
2025-06-10 22:17:54 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (930, 502) 成功
2025-06-10 22:18:28 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:18:42 | INFO     | services.airtest_poco_service:execute_adb_command:626 - 执行ADB命令 'logcat -d -t 100' 成功
2025-06-10 22:18:51 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_75qrdayfe_1749564958381 断开连接
2025-06-10 22:19:04 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_ltewhw2gw_1749565131268 断开连接
2025-06-10 22:19:50 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_i76fgqwo7_1749565144900 断开连接
2025-06-10 22:20:06 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_atkxzbbp3_1749565190957 断开连接
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 79 个元素
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222008.png
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222008.png
2025-06-10 22:20:09 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:20:10 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 78 个元素
2025-06-10 22:20:49 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_i9k8hh9dy_1749565207004 断开连接
2025-06-10 22:21:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_1yamw5oi5_1749565250613 断开连接
2025-06-10 22:21:27 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:21:29 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:21:36 | INFO     | services.airtest_poco_service:execute_adb_command:626 - 执行ADB命令 'logcat -d -t 100' 成功
2025-06-10 22:21:40 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 78 个元素
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222149.png
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222149.png
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:21:50 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 78 个元素
2025-06-10 22:21:56 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (938, 229) 成功
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 85 个元素
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222202.png
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222202.png
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:03 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 85 个元素
2025-06-10 22:22:07 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (58, 1129) 成功
2025-06-10 22:22:14 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:22:14 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 232 个元素
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222212.png
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222212.png
2025-06-10 22:22:16 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:17 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 231 个元素
2025-06-10 22:22:21 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (146, 382) 成功
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222226.png
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222226.png
2025-06-10 22:22:27 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:28 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-10 22:22:38 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:22:38 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 194 个元素
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222237.png
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222237.png
2025-06-10 22:22:39 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:22:40 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-10 22:23:13 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_po8gwrnw2_1749565285311 断开连接
2025-06-10 22:23:32 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:23:32 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:23:32 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 194 个元素
2025-06-10 22:23:32 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:23:33 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:23:33 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222330.png
2025-06-10 22:23:33 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222330.png
2025-06-10 22:23:33 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:23:33 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-10 22:23:53 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 'poco('酒店及住宿')' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:23:53 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:23:53 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('酒店及住宿')
2025-06-10 22:24:02 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 'poco(text='酒店及住宿')' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:02 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:02 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco(text='酒店及住宿')
2025-06-10 22:24:12 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 'poco(text='酒店及住宿').click()' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:12 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:12 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco(text='酒店及住宿').click()
2025-06-10 22:24:16 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 'poco(text='酒店及住宿').click' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:16 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-10 22:24:16 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco(text='酒店及住宿').click
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 194 个元素
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250610_222448.png
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250610_222448.png
2025-06-10 22:24:50 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-10 22:24:51 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-10 22:25:07 | INFO     | services.airtest_poco_service:execute_poco_query:639 - 执行Poco语句 '('酒店及住宿')' 成功
2025-06-10 22:25:07 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: ('酒店及住宿')
2025-06-10 22:25:23 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 '('酒店及住宿').click()' 失败: Cannot find any visible node by query UIObjectProxy of "酒店及住宿"
2025-06-10 22:25:23 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: Cannot find any visible node by query UIObjectProxy of "酒店及住宿"
2025-06-10 22:25:23 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: ('酒店及住宿').click()
2025-06-10 22:25:42 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-10 22:25:53 | INFO     | services.airtest_poco_service:execute_adb_command:626 - 执行ADB命令 'logcat -d -t 100' 成功
2025-06-10 22:26:34 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_x6gmyy8xa_1749565393858 断开连接
2025-06-10 22:44:03 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 03:47:38 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_ow1601ppd_1749565594975 断开连接
2025-06-11 10:57:23 | INFO     | __main__:<module>:119 - 启动DebugTools后端服务...
2025-06-11 10:58:04 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:02:26 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_g87vd3wyg_1749610684775 断开连接
2025-06-11 11:02:27 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_axcqz2tqi_1749610946685 断开连接
2025-06-11 11:02:32 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:03:56 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_86uryu6g2_1749610947928 断开连接
2025-06-11 11:04:08 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_x5mu8bcub_1749611036447 断开连接
2025-06-11 11:04:10 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:05:36 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_q87xgo96j_1749611050715 断开连接
2025-06-11 11:05:42 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:06:17 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:06:23 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:07:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_7rikc3olf_1749611142542 断开连接
2025-06-11 11:07:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_7rikc3olf_1749611142542 断开连接
2025-06-11 11:07:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_yr4vqdh9i_1749611244955 断开连接
2025-06-11 11:07:34 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:09:01 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_m8u845ham_1749611253937 断开连接
2025-06-11 11:09:03 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:09:05 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 11:09:07 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 11:09:08 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 11:09:22 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:09:22 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_110917.png
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_110917.png
2025-06-11 11:09:24 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:09:25 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:11:32 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_ppxgaz3hk_1749611341341 断开连接
2025-06-11 11:13:58 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_2mnja20ph_1749611048549 断开连接
2025-06-11 11:14:06 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:14:10 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:14:10 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_111409.png
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_111409.png
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:14:11 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:14:19 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:14:28 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_g61nhwx8s_1749611638214 断开连接
2025-06-11 11:15:56 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_rjkdfn0ez_1749611659355 断开连接
2025-06-11 11:16:32 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:32 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:32 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-11 11:16:38 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:38 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:38 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-11 11:16:39 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:39 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:39 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-11 11:16:41 | ERROR    | services.airtest_poco_service:execute_adb_command:629 - 执行ADB命令 'devices' 失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:41 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: stdout[b''] stderr[b'/system/bin/sh: devices: not found\n']
2025-06-11 11:16:41 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: devices
2025-06-11 11:16:47 | INFO     | services.airtest_poco_service:execute_adb_command:626 - 执行ADB命令 'logcat -d -t 100' 成功
2025-06-11 11:16:57 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_e9wmxsv2o_1749611757357 断开连接
2025-06-11 11:17:01 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:17:05 | INFO     | services.airtest_poco_service:disconnect_device:73 - 设备连接已断开
2025-06-11 11:17:05 | INFO     | services.device_service:disconnect_device:111 - 设备连接已断开
2025-06-11 11:17:05 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 11:17:06 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 11:17:07 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 11:18:31 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:18:31 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_111826.png
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_111826.png
2025-06-11 11:18:32 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:18:33 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:18:51 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:18:52 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:19:25 | ERROR    | services.airtest_poco_service:execute_poco_query:642 - 执行Poco语句 'poco('酒店')' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-11 11:19:25 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-11 11:19:25 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('酒店')
2025-06-11 11:19:39 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_vlyc723tf_1749611821716 断开连接
2025-06-11 11:22:26 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:22:26 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:22:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 193 个元素
2025-06-11 11:22:26 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:22:26 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:22:27 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112224.png
2025-06-11 11:22:27 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112224.png
2025-06-11 11:22:27 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:22:27 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:24:46 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_9lhmfshyq_1749611980202 断开连接
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112457.png
2025-06-11 11:24:58 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112457.png
2025-06-11 11:24:59 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:24:59 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:26:48 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_c75ignjnt_1749612286298 断开连接
2025-06-11 11:26:50 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:26:50 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:26:51 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:26:51 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:26:51 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:26:51 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112649.png
2025-06-11 11:26:51 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112649.png
2025-06-11 11:26:52 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:26:52 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:26:55 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:26:55 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:27:32 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_t5wr9xb69_1749612408328 断开连接
2025-06-11 11:27:42 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:27:42 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:27:42 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:27:42 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:27:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:27:43 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112740.png
2025-06-11 11:27:43 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112740.png
2025-06-11 11:27:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:27:44 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:29:01 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_4s0qqw514_1749612452296 断开连接
2025-06-11 11:29:04 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:29:04 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:05 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:29:05 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:29:05 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:29:05 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112902.png
2025-06-11 11:29:05 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112902.png
2025-06-11 11:29:06 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:06 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:29:11 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_iiap5qsr6_1749612541989 断开连接
2025-06-11 11:29:13 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:29:13 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112912.png
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112912.png
2025-06-11 11:29:14 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:15 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:29:54 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:29:54 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:54 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:29:54 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:29:54 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:29:55 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_112953.png
2025-06-11 11:29:55 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_112953.png
2025-06-11 11:29:55 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:29:56 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:30:01 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (642, 1197) 成功
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 45 个元素
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113011.png
2025-06-11 11:30:13 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113011.png
2025-06-11 11:30:14 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:14 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 45 个元素
2025-06-11 11:30:19 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (886, 1382) 成功
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113026.png
2025-06-11 11:30:28 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113026.png
2025-06-11 11:30:29 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:29 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:30:39 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (297, 488) 成功
2025-06-11 11:30:43 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:30:43 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:44 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 135 个元素
2025-06-11 11:30:44 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:30:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:30:44 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113041.png
2025-06-11 11:30:44 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113041.png
2025-06-11 11:30:45 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:45 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 136 个元素
2025-06-11 11:30:53 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:30:53 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 135 个元素
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113052.png
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113052.png
2025-06-11 11:30:54 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:30:55 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 135 个元素
2025-06-11 11:31:04 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (458, 176) 成功
2025-06-11 11:31:05 | ERROR    | services.airtest_poco_service:input_text_by_coordinates:516 - 在坐标 (458, 176) 输入文本失败: 'str' object is not callable
2025-06-11 11:31:08 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (458, 176) 成功
2025-06-11 11:31:08 | ERROR    | services.airtest_poco_service:input_text_by_coordinates:516 - 在坐标 (458, 176) 输入文本失败: 'str' object is not callable
2025-06-11 11:31:17 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (458, 176) 成功
2025-06-11 11:31:18 | ERROR    | services.airtest_poco_service:input_text_by_coordinates:516 - 在坐标 (458, 176) 输入文本失败: 'str' object is not callable
2025-06-11 11:33:02 | ERROR    | services.airtest_poco_service:swipe:577 - 滑动操作失败: no enough params for swipe
2025-06-11 11:33:57 | INFO     | api.operation_router:delete_all_screenshots:157 - 删除了 136 个截图文件
2025-06-11 11:34:26 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:34:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:34:41 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:34:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 196 个元素
2025-06-11 11:35:02 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_hx24izs3q_1749612551140 断开连接
2025-06-11 11:35:03 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:35:04 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 195 个元素
2025-06-11 11:36:10 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:36:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:36:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wyh9i8olk_1749612902611 断开连接
2025-06-11 11:36:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:36:53 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wyh9i8olk_1749612902611 断开连接
2025-06-11 11:37:07 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:37:07 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wyh9i8olk_1749612902611 断开连接
2025-06-11 11:37:17 | ERROR    | api.operation_router:get_dom_tree:174 - 获取DOM树失败: 
2025-06-11 11:37:24 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_wyh9i8olk_1749612902611 断开连接
2025-06-11 11:37:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_pco9g6umt_1749613044880 断开连接
2025-06-11 11:37:25 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:37:27 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:37:28 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 11:37:31 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 11:37:31 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 11:37:36 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:37:37 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 164 个元素
2025-06-11 11:37:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:37:40 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_pco9g6umt_1749613044880 断开连接
2025-06-11 11:38:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_pco9g6umt_1749613044880 断开连接
2025-06-11 11:38:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kktgrmh5k_1749611492303 断开连接
2025-06-11 11:38:41 | INFO     | __main__:<module>:119 - 启动DebugTools后端服务...
2025-06-11 11:38:55 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:38:57 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 11:38:59 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 11:39:00 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 11:39:12 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:39:12 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:39:13 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 166 个元素
2025-06-11 11:39:13 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:39:14 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:39:14 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113907.png
2025-06-11 11:39:14 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113907.png
2025-06-11 11:39:15 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:39:15 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 166 个元素
2025-06-11 11:39:25 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (514, 489) 成功
2025-06-11 11:39:28 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:39:28 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:39:29 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 136 个元素
2025-06-11 11:39:29 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:39:30 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:39:30 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_113927.png
2025-06-11 11:39:30 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_113927.png
2025-06-11 11:39:30 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:39:31 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 136 个元素
2025-06-11 11:39:37 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (352, 175) 成功
2025-06-11 11:39:39 | INFO     | services.airtest_poco_service:input_text_by_coordinates:513 - 在坐标 (352, 175) 输入文本 '123' 成功
2025-06-11 11:40:40 | INFO     | services.airtest_poco_service:swipe:578 - 向 up 滑动成功
2025-06-11 11:40:45 | INFO     | services.airtest_poco_service:swipe:578 - 向 up 滑动成功
2025-06-11 11:40:47 | INFO     | services.airtest_poco_service:swipe:578 - 向 up 滑动成功
2025-06-11 11:40:48 | INFO     | services.airtest_poco_service:swipe:578 - 向 up 滑动成功
2025-06-11 11:41:14 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_hqyluzolq_1749613130759 断开连接
2025-06-11 11:41:26 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:41:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 159 个元素
2025-06-11 11:41:31 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:41:32 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 159 个元素
2025-06-11 11:42:17 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:42:17 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:42:20 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: 
2025-06-11 11:42:20 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 11:42:32 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: 
2025-06-11 11:42:32 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 11:42:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:42:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:42:35 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: 
2025-06-11 11:42:35 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 11:42:39 | ERROR    | api.debug_router:execute_adb_command:52 - 执行ADB命令失败: 
2025-06-11 11:42:39 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 11:42:50 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:42:50 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:43:04 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:43:04 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:43:17 | ERROR    | api.operation_router:get_dom_tree:175 - 获取DOM树失败: 
2025-06-11 11:43:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:43:19 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:43:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:43:33 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:44:00 | ERROR    | api.operation_router:get_dom_tree:175 - 获取DOM树失败: 
2025-06-11 11:44:03 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:44:03 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 11:44:05 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 11:44:08 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 11:44:09 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 11:44:39 | INFO     | services.airtest_poco_service:take_screenshot:174 - 标记元素模式：正在获取DOM树...
2025-06-11 11:44:39 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 186 个元素
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:take_screenshot:178 - 正在标记元素...
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:316 - 成功在截图上标记元素
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:_mark_elements_on_screenshot:322 - 标记后的截图已保存: screenshots/screenshot_20250611_114434.png
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_114434.png
2025-06-11 11:44:41 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:44:42 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 185 个元素
2025-06-11 11:46:02 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_22qb4jjh7_1749613275278 断开连接
2025-06-11 11:46:04 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:46:30 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_s480pda0w_1749613562898 断开连接
2025-06-11 11:46:33 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:46:43 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_gabwjturh_1749613590934 断开连接
2025-06-11 11:46:44 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:46:44 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_30ur6xxla_1749613603944 断开连接
2025-06-11 11:46:45 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:46:47 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:47:19 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:47:23 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:48:05 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:48:54 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:48:55 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 186 个元素
2025-06-11 11:49:49 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:50:00 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_nydj0u8xd_1749613604958 断开连接
2025-06-11 11:50:03 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:50:16 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:50:26 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:52:45 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:52:58 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 11:53:26 | INFO     | services.airtest_poco_service:get_dom_tree:334 - 正在获取DOM树...
2025-06-11 11:53:26 | INFO     | services.airtest_poco_service:get_dom_tree:359 - DOM树获取成功，共 82 个元素
2025-06-11 11:57:22 | INFO     | services.airtest_poco_service:swipe:592 - 向 up 滑动成功
2025-06-11 11:57:31 | INFO     | services.airtest_poco_service:swipe:592 - 向 down 滑动成功
2025-06-11 11:59:15 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_kfnj2kzp8_1749613275660 断开连接
2025-06-11 11:59:15 | INFO     | main:websocket_endpoint:106 - WebSocket客户端 client_tyqavt5rt_1749613800750 断开连接
2025-06-11 12:05:13 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 12:05:13 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 12:05:13 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 12:05:15 | INFO     | main:websocket_endpoint:108 - WebSocket客户端 client_ebow5dr1m_1749614656948 断开连接
2025-06-11 12:05:16 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 12:05:18 | INFO     | main:websocket_endpoint:108 - WebSocket客户端 client_55siqo27a_1749614716095 断开连接
2025-06-11 12:05:27 | INFO     | main:websocket_endpoint:108 - WebSocket客户端 client_55siqo27a_1749614716095 断开连接
2025-06-11 12:05:35 | INFO     | main:websocket_endpoint:108 - WebSocket客户端 client_55siqo27a_1749614716095 断开连接
