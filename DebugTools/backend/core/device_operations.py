"""
设备操作抽象层
提供统一的设备操作接口，底层可以切换不同的实现（airtest+poco, appium等）
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json

@dataclass
class DeviceInfo:
    """设备信息数据类"""
    device_id: str
    name: str
    model: str
    version: str
    resolution: Tuple[int, int]
    screen_size: Tuple[float, float]
    status: str = "disconnected"

@dataclass
class ElementInfo:
    """元素信息数据类"""
    seq_index: str
    name: str
    text: str
    pos: Tuple[int, int]
    size: Tuple[int, int]
    visible: bool
    enabled: bool
    attributes: Dict[str, Any]

class DeviceOperationInterface(ABC):
    """设备操作接口抽象类"""
    
    @abstractmethod
    async def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        pass
    
    @abstractmethod
    async def disconnect_device(self) -> bool:
        """断开设备连接"""
        pass
    
    @abstractmethod
    async def get_device_info(self) -> Optional[DeviceInfo]:
        """获取设备信息"""
        pass
    
    @abstractmethod
    async def take_screenshot(self, mark_elements: bool = False) -> str:
        """截图"""
        pass
    
    @abstractmethod
    async def get_dom_tree(self) -> Dict[str, Any]:
        """获取DOM树"""
        pass
    
    @abstractmethod
    async def click_by_coordinates(self, x: int, y: int) -> bool:
        """通过坐标点击"""
        pass
    
    @abstractmethod
    async def click_by_seq_index(self, seq_index: str) -> bool:
        """通过seq_index点击"""
        pass
    
    @abstractmethod
    async def click_by_poco_query(self, poco_query: str) -> bool:
        """通过poco语句点击"""
        pass
    
    @abstractmethod
    async def input_text_by_coordinates(self, x: int, y: int, input_text: str, press_enter: bool = False) -> bool:
        """通过坐标输入文本"""
        pass

    @abstractmethod
    async def input_text_by_seq_index(self, seq_index: str, input_text: str, press_enter: bool = False) -> bool:
        """通过seq_index输入文本"""
        pass

    @abstractmethod
    async def input_text_by_poco_query(self, poco_query: str, input_text: str, press_enter: bool = False) -> bool:
        """通过poco语句输入文本"""
        pass
    
    @abstractmethod
    async def swipe(self, direction: str = "up", distance: float = 0.3) -> bool:
        """滑动操作"""
        pass
    
    @abstractmethod
    async def swipe_by_poco_query(self, poco_query: str, direction: str = "up") -> bool:
        """通过poco语句滑动"""
        pass
    
    @abstractmethod
    async def assert_element_exists(self, poco_query: str) -> bool:
        """断言元素是否存在"""
        pass
    
    @abstractmethod
    async def execute_adb_command(self, command: str) -> str:
        """执行ADB命令"""
        pass
    
    @abstractmethod
    async def execute_poco_query(self, poco_query: str) -> Any:
        """执行Poco语句"""
        pass

class DeviceOperationFactory:
    """设备操作工厂类"""
    
    _implementations = {}
    
    @classmethod
    def register_implementation(cls, name: str, implementation_class):
        """注册实现类"""
        cls._implementations[name] = implementation_class
    
    @classmethod
    def create_operation(cls, implementation_name: str = "airtest_poco") -> DeviceOperationInterface:
        """创建设备操作实例"""
        if implementation_name not in cls._implementations:
            raise ValueError(f"未找到实现: {implementation_name}")
        
        return cls._implementations[implementation_name]()
    
    @classmethod
    def get_available_implementations(cls) -> List[str]:
        """获取可用的实现列表"""
        return list(cls._implementations.keys())
