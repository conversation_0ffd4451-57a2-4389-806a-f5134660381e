#!/usr/bin/env python3
"""
ADB连接和截图测试脚本
用于诊断流媒体问题
"""

import subprocess
import base64
import os
import sys

def test_adb_devices():
    """测试ADB设备连接"""
    print("=== 测试ADB设备连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        # 解析设备列表
        lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题
        devices = []
        for line in lines:
            if line.strip() and '\t' in line:
                device_id = line.split('\t')[0]
                devices.append(device_id)
        
        print(f"发现设备: {devices}")
        return devices
    except FileNotFoundError:
        print("错误: 未找到adb命令，请确保Android SDK已安装并添加到PATH")
        return []
    except Exception as e:
        print(f"ADB设备检测失败: {e}")
        return []

def test_device_connection(device_id):
    """测试特定设备连接"""
    print(f"\n=== 测试设备 {device_id} 连接 ===")
    try:
        # 测试基本连接
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'echo', 'test'], 
                              capture_output=True, text=True, timeout=5)
        print(f"连接测试返回码: {result.returncode}")
        print(f"输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ 设备连接正常")
            return True
        else:
            print("❌ 设备连接失败")
            return False
    except Exception as e:
        print(f"设备连接测试失败: {e}")
        return False

def test_screenshot(device_id):
    """测试截图功能"""
    print(f"\n=== 测试设备 {device_id} 截图 ===")
    try:
        # 测试截图命令
        cmd = ['adb', '-s', device_id, 'exec-out', 'screencap', '-p']
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, timeout=10)
        print(f"截图命令返回码: {result.returncode}")
        print(f"stdout长度: {len(result.stdout) if result.stdout else 0}")
        if result.stderr:
            print(f"stderr: {result.stderr.decode()}")
        
        if result.returncode == 0 and result.stdout:
            print("✅ 截图命令执行成功")
            
            # 保存截图到文件
            screenshot_path = f"test_screenshot_{device_id.replace(':', '_')}.png"
            with open(screenshot_path, 'wb') as f:
                f.write(result.stdout)
            print(f"截图已保存到: {screenshot_path}")
            
            # 测试base64编码
            image_data = base64.b64encode(result.stdout).decode('utf-8')
            print(f"Base64编码长度: {len(image_data)}")
            print(f"Base64前50字符: {image_data[:50]}...")
            
            return True
        else:
            print("❌ 截图命令失败")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 截图命令超时")
        return False
    except Exception as e:
        print(f"截图测试失败: {e}")
        return False

def test_screen_info(device_id):
    """测试获取屏幕信息"""
    print(f"\n=== 测试设备 {device_id} 屏幕信息 ===")
    try:
        # 获取屏幕尺寸
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'wm', 'size'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"屏幕尺寸: {result.stdout.strip()}")
        
        # 获取屏幕密度
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'wm', 'density'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"屏幕密度: {result.stdout.strip()}")
        
        return True
    except Exception as e:
        print(f"屏幕信息获取失败: {e}")
        return False

def main():
    """主测试函数"""
    print("DebugTools ADB连接和截图测试")
    print("=" * 50)
    
    # 检查ADB是否可用
    devices = test_adb_devices()
    if not devices:
        print("\n❌ 未发现任何设备，请检查：")
        print("1. 设备是否已连接")
        print("2. USB调试是否已开启")
        print("3. 是否已授权调试")
        print("4. ADB是否正确安装")
        return
    
    # 测试每个设备
    for device_id in devices:
        print(f"\n{'='*20} 测试设备 {device_id} {'='*20}")
        
        # 测试连接
        if not test_device_connection(device_id):
            continue
        
        # 测试屏幕信息
        test_screen_info(device_id)
        
        # 测试截图
        if test_screenshot(device_id):
            print(f"✅ 设备 {device_id} 所有测试通过")
        else:
            print(f"❌ 设备 {device_id} 截图测试失败")
    
    print(f"\n{'='*50}")
    print("测试完成")

if __name__ == "__main__":
    main()
