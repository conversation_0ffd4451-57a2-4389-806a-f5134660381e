/**
 * 屏幕流媒体API
 */

import request from '@/utils/request'

export const streamingApi = {
  // 开始流媒体
  startStreaming: (data) => {
    return request({
      url: '/api/streaming/start',
      method: 'post',
      data
    })
  },

  // 停止流媒体
  stopStreaming: () => {
    return request({
      url: '/api/streaming/stop',
      method: 'post'
    })
  },

  // 获取流媒体状态
  getStatus: () => {
    return request({
      url: '/api/streaming/status',
      method: 'get'
    })
  },

  // 开始录制
  startRecording: (data) => {
    return request({
      url: '/api/streaming/recording/start',
      method: 'post',
      data
    })
  },

  // 停止录制
  stopRecording: () => {
    return request({
      url: '/api/streaming/recording/stop',
      method: 'post'
    })
  },

  // 截图
  takeScreenshot: (data) => {
    return request({
      url: '/api/streaming/screenshot',
      method: 'post',
      data
    })
  },

  // 获取录制文件列表
  getRecordings: () => {
    return request({
      url: '/api/streaming/recordings',
      method: 'get'
    })
  },

  // 下载录制文件
  downloadRecording: (filename) => {
    return `/api/streaming/recordings/${filename}`
  },

  // 删除录制文件
  deleteRecording: (filename) => {
    return request({
      url: `/api/streaming/recordings/${filename}`,
      method: 'delete'
    })
  },

  // 获取截图列表
  getScreenshots: () => {
    return request({
      url: '/api/streaming/screenshots',
      method: 'get'
    })
  },

  // 下载截图
  downloadScreenshot: (filename) => {
    return `/api/streaming/screenshots/${filename}`
  },

  // 删除截图
  deleteScreenshot: (filename) => {
    return request({
      url: `/api/streaming/screenshots/${filename}`,
      method: 'delete'
    })
  }
}

/**
 * WebSocket流媒体客户端
 */
export class StreamingWebSocket {
  constructor() {
    this.ws = null
    this.isConnected = false
    this.onFrameCallback = null
    this.onStatusCallback = null
    this.onErrorCallback = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
  }

  connect() {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.host}/api/streaming/ws`

      console.log('连接WebSocket:', wsUrl)
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('流媒体WebSocket连接成功')
        this.isConnected = true
        this.reconnectAttempts = 0

        if (this.onStatusCallback) {
          this.onStatusCallback('connected')
        }
      }
      
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('收到WebSocket消息:', message.type, message.timestamp)

          if (message.type === 'frame' && this.onFrameCallback) {
            this.onFrameCallback(message)
          } else if (message.type === 'connection') {
            console.log('WebSocket连接确认:', message.message)
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error, event.data)
        }
      }
      
      this.ws.onclose = () => {
        console.log('流媒体WebSocket连接关闭')
        this.isConnected = false
        
        if (this.onStatusCallback) {
          this.onStatusCallback('disconnected')
        }
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++
            console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
            this.connect()
          }, this.reconnectDelay * this.reconnectAttempts)
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('流媒体WebSocket错误:', error)

        if (this.onErrorCallback) {
          this.onErrorCallback(error)
        }
      }
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      
      if (this.onErrorCallback) {
        this.onErrorCallback(error)
      }
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.isConnected = false
  }

  // 设置帧数据回调
  onFrame(callback) {
    this.onFrameCallback = callback
  }

  // 设置状态回调
  onStatus(callback) {
    this.onStatusCallback = callback
  }

  // 设置错误回调
  onError(callback) {
    this.onErrorCallback = callback
  }

  // 发送消息
  send(message) {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message))
    }
  }
}

/**
 * 视频录制器（基于MediaRecorder API）
 */
export class CanvasRecorder {
  constructor(canvas) {
    this.canvas = canvas
    this.mediaRecorder = null
    this.recordedChunks = []
    this.isRecording = false
  }

  async startRecording(options = {}) {
    if (this.isRecording) {
      throw new Error('录制已在进行中')
    }

    try {
      // 获取canvas流
      const stream = this.canvas.captureStream(options.fps || 30)
      
      // 创建MediaRecorder
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: options.mimeType || 'video/webm;codecs=vp9'
      })
      
      this.recordedChunks = []
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data)
        }
      }
      
      this.mediaRecorder.onstop = () => {
        this.isRecording = false
      }
      
      this.mediaRecorder.start()
      this.isRecording = true
      
      console.log('Canvas录制开始')
      
    } catch (error) {
      console.error('开始Canvas录制失败:', error)
      throw error
    }
  }

  stopRecording() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording || !this.mediaRecorder) {
        reject(new Error('当前没有录制任务'))
        return
      }

      this.mediaRecorder.onstop = () => {
        this.isRecording = false
        
        // 创建Blob
        const blob = new Blob(this.recordedChunks, {
          type: 'video/webm'
        })
        
        console.log('Canvas录制完成')
        resolve(blob)
      }

      this.mediaRecorder.stop()
    })
  }

  // 下载录制的视频
  downloadRecording(blob, filename) {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename || `canvas_recording_${Date.now()}.webm`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}
