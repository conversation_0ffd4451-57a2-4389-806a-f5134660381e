<template>
  <a-layout class="layout">
    <!-- 顶部导航 -->
    <a-layout-header class="header">
      <div class="header-content">
        <div class="logo">
          <icon-mobile />
          <span>DebugTools</span>
        </div>

        <!-- 顶部导航菜单 -->
        <div class="top-menu">
          <a-menu
            :selected-keys="getSelectedKeys()"
            mode="horizontal"
            @menu-item-click="onMenuClick"
            class="header-menu"
          >
            <a-sub-menu key="android">
              <template #icon><icon-mobile /></template>
              <template #title>Android调试工具</template>

              <a-menu-item key="/device">
                <template #icon><icon-settings /></template>
                设备管理
              </a-menu-item>

              <a-menu-item key="/package">
                <template #icon><icon-apps /></template>
                安装包管理
              </a-menu-item>

              <a-menu-item key="/operation">
                <template #icon><icon-interaction /></template>
                业务操作
              </a-menu-item>
            </a-sub-menu>

            <a-menu-item key="/logs">
              <template #icon><icon-file-text /></template>
              日志管理
            </a-menu-item>
          </a-menu>
        </div>

        <!-- 设备状态和用户操作 -->
        <div class="header-right">
          <div class="device-status">
            <a-badge :status="deviceStore.isConnected ? 'processing' : 'default'">
              <span>{{ deviceStore.isConnected ? `已连接: ${deviceStore.currentDeviceId}` : '未连接设备' }}</span>
            </a-badge>
          </div>

          <div class="user-actions">
            <a-button type="text" @click="toggleTheme">
              <icon-sun v-if="isDark" />
              <icon-moon v-else />
            </a-button>
          </div>
        </div>
      </div>
    </a-layout-header>

    <!-- 主内容区 -->
    <a-layout-content class="content">
      <div class="content-wrapper">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDeviceStore } from '@/stores/device'
import { useWebSocket } from '@/utils/websocket'

const router = useRouter()
const route = useRoute()
const deviceStore = useDeviceStore()

const isDark = ref(false)

const currentRoute = computed(() => route.path)

// 获取选中的菜单键
const getSelectedKeys = () => {
  const path = route.path
  if (['/device', '/package', '/operation'].includes(path)) {
    return ['android']
  }
  return [path]
}

// WebSocket连接
const { connect } = useWebSocket()

const onMenuClick = (key) => {
  router.push(key)
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.body.setAttribute('arco-theme', isDark.value ? 'dark' : '')
}

onMounted(() => {
  // 连接WebSocket
  connect()

  // 初始化设备状态
  deviceStore.fetchDeviceStatus()
})
</script>

<style scoped>
.layout {
  min-height: 100vh;
}

.header {
  background: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
  padding: 0 20px;
  height: 64px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--color-text-1);
  margin-right: 40px;
}

.logo span {
  margin-left: 8px;
}

.top-menu {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.header-menu {
  background: transparent;
  border-bottom: none;
}

.header-menu :deep(.arco-menu-item) {
  border-radius: 4px;
  margin: 0 4px;
}

.header-menu :deep(.arco-menu-item-selected) {
  background: var(--color-primary-light-1);
  color: var(--color-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.device-status {
  display: flex;
  align-items: center;
}

.user-actions {
  display: flex;
  align-items: center;
}

.content {
  background: var(--color-bg-1);
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

.content-wrapper {
  background: var(--color-bg-2);
  border-radius: 6px;
  padding: 20px;
  min-height: calc(100vh - 104px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-wrap: wrap;
  }

  .top-menu {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }

  .header {
    height: auto;
    padding: 10px 20px;
  }

  .content {
    height: auto;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .logo {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .top-menu {
    width: 100%;
    margin-top: 0;
  }

  .header-menu :deep(.arco-menu-item) {
    margin: 0 2px;
    font-size: 12px;
  }
}
</style>
