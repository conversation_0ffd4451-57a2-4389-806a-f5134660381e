<template>
  <div class="operation-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">业务操作</h2>
    </div>

    <!-- 第一行：截图和DOM树 -->
    <a-row :gutter="16" class="first-row">
      <!-- 左侧：设备截图/实时流媒体 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card class="screenshot-card">
          <template #title>
            <div class="screen-mode-tabs">
              <a-radio-group v-model="screenDisplayMode" type="button" size="small">
                <a-radio value="screenshot">设备截图</a-radio>
                <a-radio value="streaming">实时流媒体</a-radio>
              </a-radio-group>
            </div>
          </template>
          <template #extra>
            <a-space v-if="screenDisplayMode === 'screenshot'" size="small">
              <a-button
                type="primary"
                @click="takeScreenshot(false)"
                :loading="screenshotLoading"
                :disabled="!deviceStore.isConnected"
                size="small"
              >
                <template #icon><icon-camera /></template>
                截图
              </a-button>
              <a-button
                @click="takeScreenshot(true)"
                :loading="screenshotLoading"
                :disabled="!deviceStore.isConnected"
                size="small"
              >
                <template #icon><icon-camera /></template>
                截图并标记元素
              </a-button>
              <a-button
                @click="openScreenshotManager"
                :disabled="!deviceStore.isConnected"
                size="small"
              >
                <template #icon><icon-folder /></template>
                截图管理
              </a-button>
            </a-space>
          </template>

          <!-- 截图模式 -->
          <div v-if="screenDisplayMode === 'screenshot'" class="screenshot-section">
            <div v-if="currentScreenshot" class="screenshot-content">
              <div class="screenshot-header">
                <div class="screenshot-actions">
                  <a-button-group size="small">
                    <a-button @click="zoomOut" :disabled="zoomLevel <= 0.2">
                      <template #icon><icon-minus /></template>
                    </a-button>
                    <a-button @click="resetZoom">
                      {{ Math.round(zoomLevel * 100) }}%
                    </a-button>
                    <a-button @click="zoomIn" :disabled="zoomLevel >= 3">
                      <template #icon><icon-plus /></template>
                    </a-button>
                  </a-button-group>
                  <a-button @click="fitToContainer" size="small" style="margin-left: 8px">
                    <template #icon><icon-fullscreen /></template>
                    自适应
                  </a-button>
                </div>
              </div>

              <div class="screenshot-container">
                <div class="screenshot-wrapper">
                  <img
                    :src="screenshotUrl"
                    alt="设备截图"
                    class="screenshot-image"
                    :style="{ transform: `scale(${zoomLevel})` }"
                    @click="onScreenshotClick"
                  />
                </div>
              </div>
            </div>
            <div v-else class="no-screenshot">
              <a-empty description="暂无截图，请先截图" />
            </div>
          </div>

          <!-- 实时流媒体模式 -->
          <div v-else-if="screenDisplayMode === 'streaming'" class="streaming-section">
            <div class="streaming-placeholder">
              <a-empty description="实时流媒体已在悬浮窗口中显示">
                <template #image>
                  <icon-mobile style="font-size: 64px; color: var(--color-text-3);" />
                </template>
              </a-empty>
              <div class="streaming-actions">
                <a-button
                  v-if="!showFloatingWindow"
                  type="primary"
                  @click="openFloatingWindow"
                  :disabled="!deviceStore.isConnected"
                  size="large"
                >
                  <template #icon><icon-play-arrow /></template>
                  打开悬浮流媒体窗口
                </a-button>
                <a-button
                  v-else
                  @click="closeFloatingWindow"
                  size="large"
                >
                  <template #icon><icon-close /></template>
                  关闭悬浮窗口
                </a-button>
              </div>
              <div v-if="showFloatingWindow" class="streaming-tips">
                <a-alert
                  type="info"
                  message="提示"
                  description="点击悬浮窗口中的设备屏幕可以获取坐标，坐标会自动填充到下方的操作表单中。"
                  show-icon
                  closable
                />
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：DOM树 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="DOM树结构" class="dom-tree-card">
          <template #extra>
            <div class="dom-tree-actions">
              <a-button
                @click="expandAllDomNodes"
                :disabled="!domTree || !domTree.elements || domTree.elements.length === 0"
                size="small"
                style="margin-right: 8px"
              >
                <template #icon><icon-down /></template>
                展开全部
              </a-button>
              <a-button
                @click="collapseAllDomNodes"
                :disabled="!domTree || !domTree.elements || domTree.elements.length === 0"
                size="small"
                style="margin-right: 8px"
              >
                <template #icon><icon-up /></template>
                收起全部
              </a-button>
              <a-button
                @click="getDomTree"
                :loading="domLoading"
                :disabled="!deviceStore.isConnected"
                size="small"
              >
                <template #icon><icon-code /></template>
                获取DOM树
              </a-button>
            </div>
          </template>

          <div class="dom-tree-section">
            <div v-if="domTree && domTree.elements && domTree.elements.length > 0" class="dom-tree-content">
              <div class="dom-tree-header">
                <a-input
                  v-model="searchKeyword"
                  placeholder="搜索元素..."
                  size="small"
                  @input="searchDomTree"
                >
                  <template #prefix><icon-search /></template>
                </a-input>
              </div>

              <div class="dom-tree-container">
                <a-tree
                  ref="domTreeRef"
                  :data="filteredDomTree"
                  :show-line="true"
                  :block-node="true"
                  v-model:expanded-keys="expandedKeys"
                  @select="onDomNodeSelect"
                >
                  <template #title="nodeData">
                    <div class="dom-node-title" @dblclick="toggleDomNodeExpand(nodeData.key)">
                      <span class="seq-index">{{ nodeData.seq_index || nodeData.key }}</span>
                      <span class="node-name">{{ nodeData.title || nodeData.name || 'Unknown' }}</span>
                      <span v-if="nodeData.text" class="node-text">({{ nodeData.text }})</span>
                      <a-tag v-if="!nodeData.visible" color="red" size="small">隐藏</a-tag>
                    </div>
                  </template>
                </a-tree>
              </div>
            </div>
            <div v-else class="no-dom-tree">
              <a-empty description="暂无DOM树，请先获取DOM树" />
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 第二行：元素详情和操作面板 -->
    <a-row :gutter="16" class="second-row">
      <!-- 左侧：元素详情 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="元素详情" class="element-details-card">
          <template #extra>
            <div v-if="selectedElement" class="element-actions-header">
              <a-button
                size="small"
                type="primary"
                @click="fillClickForm"
                style="margin-right: 8px"
              >
                填充到点击操作
              </a-button>
              <a-button
                size="small"
                type="outline"
                @click="fillInputForm"
              >
                填充到输入操作
              </a-button>
            </div>
          </template>

          <div class="element-details-content">
            <div v-if="selectedElement">
              <a-table
                :data="getElementDetailsData(selectedElement)"
                :columns="elementDetailsColumns"
                :pagination="false"
                size="small"
                :scroll="{ y: 350 }"
                :stripe="true"
              />
            </div>
            <div v-else class="no-element-selected">
              <a-empty description="请选择一个DOM元素" />
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：操作面板 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card class="operation-card">
          <template #title>
            <div class="operation-card-title">
              <span>操作面板</span>
              <!-- 自动截图设置 -->
              <div class="auto-screenshot-setting">
                <a-tooltip content="启用后，每次操作成功后会自动截图并标记元素">
                  <a-switch
                    v-model="autoScreenshotEnabled"
                    size="small"
                  >
                    <template #checked>
                      <icon-camera />
                    </template>
                    <template #unchecked>
                      <icon-camera />
                    </template>
                  </a-switch>
                </a-tooltip>
                <span class="setting-label">操作后自动截图</span>
              </div>
            </div>
          </template>

          <div class="operation-content">
            <a-tabs v-model:active-key="activeTab" type="card" size="small">
            <!-- 点击操作 -->
            <a-tab-pane key="click" title="点击操作">
              <a-form :model="clickForm" layout="vertical" size="small">
                <a-form-item label="点击方式">
                  <a-radio-group v-model="clickForm.method" size="small">
                    <a-radio value="coordinates">坐标点击</a-radio>
                    <a-radio value="seq_index">索引点击</a-radio>
                    <a-radio value="poco_query">Poco查询</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <template v-if="clickForm.method === 'coordinates'">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <a-form-item label="X坐标">
                        <a-input-number v-model="clickForm.x" :min="0" size="small" style="width: 100%" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="Y坐标">
                        <a-input-number v-model="clickForm.y" :min="0" size="small" style="width: 100%" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
                
                <a-form-item v-if="clickForm.method === 'seq_index'" label="元素索引">
                  <a-input v-model="clickForm.seq_index" placeholder="如: 1.2.3" size="small" />
                </a-form-item>
                
                <a-form-item v-if="clickForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea
                    v-model="clickForm.poco_query"
                    placeholder="如: poco('com.example:id/button').click()"
                    :rows="2"
                    size="small"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performClick"
                    :loading="clickLoading"
                    :disabled="!deviceStore.isConnected"
                    size="small"
                    block
                  >
                    执行点击
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- 输入操作 -->
            <a-tab-pane key="input" title="输入操作">
              <a-form :model="inputForm" layout="vertical" size="small">
                <a-form-item label="输入方式">
                  <a-radio-group v-model="inputForm.method" size="small">
                    <a-radio value="coordinates">坐标输入</a-radio>
                    <a-radio value="seq_index">索引输入</a-radio>
                    <a-radio value="poco_query">Poco查询</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <template v-if="inputForm.method === 'coordinates'">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <a-form-item label="X坐标">
                        <a-input-number v-model="inputForm.x" :min="0" size="small" style="width: 100%" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="Y坐标">
                        <a-input-number v-model="inputForm.y" :min="0" size="small" style="width: 100%" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
                
                <a-form-item v-if="inputForm.method === 'seq_index'" label="元素索引">
                  <a-input v-model="inputForm.seq_index" placeholder="如: 1.2.3" size="small" />
                </a-form-item>
                
                <a-form-item v-if="inputForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea
                    v-model="inputForm.poco_query"
                    placeholder="如: poco('com.example:id/edittext')"
                    :rows="2"
                    size="small"
                  />
                </a-form-item>
                
                <a-form-item label="输入内容" required>
                  <a-textarea v-model="inputForm.text" placeholder="请输入要输入的文本" size="small" />
                </a-form-item>

                <a-form-item>
                  <a-checkbox v-model="inputForm.press_enter" size="small">
                    输入完成后按回车键
                  </a-checkbox>
                </a-form-item>

                <a-form-item>
                  <a-button
                    type="primary"
                    @click="performInput"
                    :loading="inputLoading"
                    :disabled="!deviceStore.isConnected || !inputForm.text"
                    size="small"
                    block
                  >
                    执行输入
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- 滑动操作 -->
            <a-tab-pane key="swipe" title="滑动操作">
              <a-form :model="swipeForm" layout="vertical" size="small">
                <a-form-item label="滑动方式">
                  <a-radio-group v-model="swipeForm.method" size="small">
                    <a-radio value="default">屏幕滑动</a-radio>
                    <a-radio value="poco_query">元素滑动</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="滑动方向">
                  <a-select v-model="swipeForm.direction" size="small">
                    <a-option value="up">向上</a-option>
                    <a-option value="down">向下</a-option>
                    <a-option value="left">向左</a-option>
                    <a-option value="right">向右</a-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item v-if="swipeForm.method === 'default'" label="滑动距离">
                  <a-row :gutter="8">
                    <a-col :span="16">
                      <a-slider
                        v-model="swipeForm.distance"
                        :min="0.1"
                        :max="1"
                        :step="0.1"
                        :format-tooltip="(value) => `${Math.round(value * 100)}%`"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="8">
                      <a-input-number
                        v-model="swipeForm.distance"
                        :min="0.1"
                        :max="1"
                        :step="0.1"
                        :precision="1"
                        size="small"
                        style="width: 100%"
                      />
                    </a-col>
                  </a-row>
                </a-form-item>
                
                <a-form-item v-if="swipeForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea
                    v-model="swipeForm.poco_query"
                    placeholder="如: poco('com.example:id/scrollview')"
                    :rows="2"
                    size="small"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performSwipe"
                    :loading="swipeLoading"
                    :disabled="!deviceStore.isConnected"
                    size="small"
                    block
                  >
                    执行滑动
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- Poco调试 -->
            <a-tab-pane key="poco_debug" title="Poco调试">
              <a-form :model="pocoDebugForm" layout="vertical" size="small">
                <a-form-item label="Poco语句" required>
                  <a-textarea
                    v-model="pocoDebugForm.poco_statement"
                    placeholder="如: poco('com.example:id/button').click()"
                    :rows="3"
                    size="small"
                  />
                </a-form-item>

                <div class="poco-help-text">
                  支持格式：poco(xxxxx).xxx.xxx.xxx
                </div>

                <a-form-item>
                  <a-button
                    type="primary"
                    @click="executePocoDebug"
                    :loading="pocoDebugLoading"
                    :disabled="!deviceStore.isConnected || !pocoDebugForm.poco_statement"
                    size="small"
                    block
                  >
                    执行Poco语句
                  </a-button>
                </a-form-item>

                <div v-if="pocoDebugResult" class="poco-debug-result">
                  <a-divider>执行结果</a-divider>
                  <a-textarea
                    :value="pocoDebugResult"
                    :rows="4"
                    readonly
                    class="result-textarea"
                    size="small"
                  />
                </div>
              </a-form>
            </a-tab-pane>
            </a-tabs>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 第三行：ADB命令调试 + 设备日志 -->
    <a-row :gutter="16" class="third-row">
      <!-- 左侧：ADB命令调试 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="ADB命令调试" class="adb-debug-card">
          <div class="adb-debug-content">
            <!-- ADB命令输入 -->
            <div class="command-input-section">
              <a-input-group compact>
                <a-input
                  addon-before="adb shell"
                  v-model="adbCommand"
                  placeholder="输入shell命令，如: getprop ro.build.version.sdk"
                  @keyup.enter="executeAdbCommand"
                  :disabled="!deviceStore.isConnected"
                  size="small"
                  style="width: calc(100% - 60px)"
                />
                <a-button
                  type="primary"
                  @click="executeAdbCommand"
                  :loading="adbLoading"
                  :disabled="!deviceStore.isConnected || !adbCommand.trim()"
                  size="small"
                  style="width: 60px"
                >
                  执行
                </a-button>
              </a-input-group>
            </div>

            <!-- ADB命令结果 -->
            <div class="command-result-section">
              <div class="result-header">
                <h4>执行结果</h4>
                <a-button size="small" @click="clearAdbResult">
                  <template #icon><icon-delete /></template>
                  清空
                </a-button>
              </div>
              <div class="result-container">
                <pre class="result-text">{{ adbResult || '暂无执行结果' }}</pre>
              </div>
            </div>

            <!-- ADB历史记录 -->
            <div class="command-history-section">
              <div class="history-header">
                <h4>历史记录</h4>
                <a-button size="small" @click="refreshAdbHistory" :loading="loadingAdbHistory">
                  <template #icon><icon-refresh /></template>
                  刷新
                </a-button>
              </div>
              <div class="history-container">
                <a-list :data="adbHistory" :loading="loadingAdbHistory" size="small">
                  <template #item="{ item }">
                    <a-list-item class="history-item">
                      <div class="history-content" @click="selectAdbCommand(item.command)">
                        <div class="history-command">
                          <icon-code />
                          <span>{{ item.command }}</span>
                        </div>
                        <div class="history-time">{{ formatTime(item.timestamp) }}</div>
                      </div>
                    </a-list-item>
                  </template>

                  <template #empty>
                    <a-empty description="暂无历史记录" />
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：设备日志 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="设备日志" class="device-logs-card">
          <template #extra>
            <div class="log-actions">
              <a-button
                size="small"
                @click="getDeviceLogs"
                :loading="logLoading"
                :disabled="!deviceStore.isConnected"
              >
                <template #icon><icon-refresh /></template>
                刷新
              </a-button>
              <a-button
                size="small"
                @click="clearDeviceLogs"
                :disabled="!deviceStore.isConnected"
              >
                <template #icon><icon-delete /></template>
                清空
              </a-button>
            </div>
          </template>

          <div class="device-logs-content">
            <a-textarea
              v-model="deviceLogs"
              readonly
              placeholder="设备日志将显示在这里..."
              class="logs-textarea"
            />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 截图管理模态框 -->
    <a-modal
      v-model:visible="showScreenshotManager"
      title="截图管理"
      width="800px"
      :footer="false"
    >
      <div class="screenshot-manager">
        <div class="manager-header">
          <a-button
            type="primary"
            @click="refreshScreenshots"
            :loading="loadingScreenshots"
          >
            <template #icon><icon-refresh /></template>
            刷新
          </a-button>
          <a-popconfirm
            content="确定要删除所有截图吗？此操作不可恢复！"
            @ok="deleteAllScreenshots"
          >
            <a-button status="danger" type="outline">
              <template #icon><icon-delete /></template>
              删除所有
            </a-button>
          </a-popconfirm>
        </div>

        <div class="screenshots-grid">
          <div
            v-for="screenshot in screenshots"
            :key="screenshot.filename"
            class="screenshot-item"
          >
            <div class="screenshot-preview">
              <img
                :src="`/api/android/operation/screenshot/${screenshot.filename}`"
                :alt="screenshot.filename"
                @click="viewScreenshot(screenshot)"
              />
            </div>
            <div class="screenshot-info">
              <div class="filename">{{ screenshot.filename }}</div>
              <div class="time">{{ formatTime(screenshot.created_time) }}</div>
              <div class="size">{{ formatFileSize(screenshot.size) }}</div>
            </div>
            <div class="screenshot-actions">
              <a-button
                size="small"
                @click="viewScreenshot(screenshot)"
              >
                查看
              </a-button>
              <a-popconfirm
                content="确定要删除这个截图吗？"
                @ok="deleteScreenshot(screenshot.filename)"
              >
                <a-button size="small" status="danger" type="outline">
                  删除
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <a-empty v-if="screenshots.length === 0 && !loadingScreenshots" description="暂无截图" />
      </div>
    </a-modal>

    <!-- 截图查看模态框 -->
    <a-modal
      v-model:visible="showScreenshotViewer"
      :title="currentViewingScreenshot?.filename"
      width="90%"
      :footer="false"
    >
      <div class="screenshot-viewer" v-if="currentViewingScreenshot">
        <img
          :src="`/api/android/operation/screenshot/${currentViewingScreenshot.filename}`"
          :alt="currentViewingScreenshot.filename"
          style="max-width: 100%; height: auto;"
        />
      </div>
    </a-modal>

    <!-- 悬浮流媒体窗口 -->
    <FloatingStreamWindow
      :visible="showFloatingWindow"
      @close="onFloatingWindowClose"
      @coordinate-click="onCoordinateClick"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { operationApi } from '@/api/operation'
import { debugApi } from '@/api/debug'
import { Message } from '@arco-design/web-vue'
import FloatingStreamWindow from '@/components/FloatingStreamWindow.vue'

const deviceStore = useDeviceStore()

// 响应式数据
const activeTab = ref('click')
const screenDisplayMode = ref('screenshot') // 'screenshot' 或 'streaming'
const showFloatingWindow = ref(false)
const currentScreenshot = ref('')
const domTree = ref(null)
const searchKeyword = ref('')
const zoomLevel = ref(1)
const selectedElement = ref(null)
const domTreeRef = ref(null)
const expandedKeys = ref([])

// 设备日志相关
const deviceLogs = ref('')
const logLoading = ref(false)

// ADB调试相关
const adbCommand = ref('')
const adbResult = ref('')
const adbHistory = ref([])
const adbLoading = ref(false)
const loadingAdbHistory = ref(false)

// Poco调试相关
const pocoDebugForm = reactive({
  poco_statement: ''
})
const pocoDebugResult = ref('')
const pocoDebugLoading = ref(false)

// 截图管理相关
const showScreenshotManager = ref(false)
const showScreenshotViewer = ref(false)
const screenshots = ref([])
const currentViewingScreenshot = ref(null)
const loadingScreenshots = ref(false)

// 自动截图设置
const autoScreenshotEnabled = ref(false)
const autoScreenshotDelay = 5000 // 5秒延时

// 加载状态
const screenshotLoading = ref(false)
const domLoading = ref(false)
const clickLoading = ref(false)
const inputLoading = ref(false)
const swipeLoading = ref(false)

// 表单数据
const clickForm = reactive({
  method: 'coordinates',
  x: 0,
  y: 0,
  seq_index: '',
  poco_query: ''
})

const inputForm = reactive({
  method: 'coordinates',
  x: 0,
  y: 0,
  seq_index: '',
  poco_query: '',
  text: '',
  press_enter: false
})

const swipeForm = reactive({
  method: 'default',
  direction: 'up',
  distance: 0.3,
  poco_query: ''
})

// 元素详情表格列定义
const elementDetailsColumns = [
  {
    title: '字段名',
    dataIndex: 'field',
    width: 140,
    render: ({ record }) => {
      return h('span', { style: { fontWeight: 'bold', color: 'var(--color-text-1)' } }, record.field)
    }
  },
  {
    title: '字段值',
    dataIndex: 'value',
    render: ({ record }) => {
      if (record.type === 'tag') {
        return h('a-tag', { color: record.color }, record.value)
      } else if (record.type === 'code') {
        return h('code', { style: { fontSize: '11px', background: 'var(--color-fill-1)', padding: '2px 4px', borderRadius: '2px' } }, record.value)
      }
      return record.value
    }
  }
]

// 计算属性
const screenshotUrl = computed(() => {
  if (currentScreenshot.value) {
    return `/api/android/operation/screenshot/${currentScreenshot.value.split('/').pop()}`
  }
  return ''
})

const filteredDomTree = computed(() => {
  if (!domTree.value || !domTree.value.elements || !Array.isArray(domTree.value.elements)) {
    console.log('DOM树数据无效:', domTree.value)
    return []
  }

  const elements = domTree.value.elements

  if (!searchKeyword.value) {
    return convertDomTreeToTreeData(elements)
  }

  // 搜索过滤逻辑
  const keyword = searchKeyword.value.toLowerCase()
  const filteredElements = elements.filter(element => {
    if (!element) return false

    return String(element.seq_index || '').includes(keyword) ||
           String(element.index || '').toLowerCase().includes(keyword) ||
           (element.name && element.name.toLowerCase().includes(keyword)) ||
           (element.class_name && element.class_name.toLowerCase().includes(keyword)) ||
           (element.text && element.text.toLowerCase().includes(keyword)) ||
           (element.attributes && element.attributes.class && element.attributes.class.toLowerCase().includes(keyword)) ||
           (element.attributes && element.attributes.resource_id && element.attributes.resource_id.toLowerCase().includes(keyword))
  })

  return convertDomTreeToTreeData(filteredElements)
})

// 方法
const takeScreenshot = async (markElements = false) => {
  screenshotLoading.value = true
  try {
    const response = await operationApi.takeScreenshot({ mark_elements: markElements })
    currentScreenshot.value = response.data.file_path
    Message.success('截图成功')

    // 如果是标记元素的截图，同时获取DOM树
    if (markElements) {
      // 等待一小段时间确保截图完成
      setTimeout(async () => {
        await getDomTree()
      }, 500)
    }
  } catch (error) {
    Message.error('截图失败: ' + error.message)
  } finally {
    screenshotLoading.value = false
  }
}

const getDomTree = async () => {
  domLoading.value = true
  try {
    const response = await operationApi.getDomTree()
    console.log('DOM树响应:', response.data)
    domTree.value = response.data

    if (response.data && response.data.elements && response.data.elements.length > 0) {
      Message.success(`获取DOM树成功，共 ${response.data.total_elements || response.data.elements.length} 个元素`)
    } else {
      Message.warning('获取DOM树成功，但没有找到元素')
      console.log('DOM树数据结构:', response.data)
    }
  } catch (error) {
    Message.error('获取DOM树失败: ' + error.message)
    console.error('DOM树获取错误:', error)
  } finally {
    domLoading.value = false
  }
}

const performClick = async () => {
  clickLoading.value = true
  try {
    const response = await operationApi.clickElement(clickForm)
    if (response.data.success) {
      Message.success('点击操作成功')

      // 如果启用了自动截图，等待5秒后执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        setTimeout(async () => {
          await takeScreenshot(true)
        }, autoScreenshotDelay)
      }
    } else {
      Message.error('点击操作失败')
    }
  } catch (error) {
    Message.error('点击操作失败: ' + error.message)
  } finally {
    clickLoading.value = false
  }
}

const performInput = async () => {
  inputLoading.value = true
  try {
    console.log('发送输入请求:', inputForm)
    const response = await operationApi.inputText(inputForm)
    console.log('输入响应:', response)

    if (response.data && response.data.success) {
      Message.success('输入操作成功')

      // 如果启用了自动截图，等待5秒后执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        setTimeout(async () => {
          await takeScreenshot(true)
        }, autoScreenshotDelay)
      }
    } else {
      const errorMsg = response.data?.message || '输入操作失败'
      Message.error(errorMsg)
    }
  } catch (error) {
    console.error('输入操作错误:', error)
    const errorMsg = error.response?.data?.detail || error.message || '输入操作失败'
    Message.error('输入操作失败: ' + errorMsg)
  } finally {
    inputLoading.value = false
  }
}

const performSwipe = async () => {
  swipeLoading.value = true
  try {
    const response = await operationApi.swipeScreen(swipeForm)
    if (response.data.success) {
      Message.success('滑动操作成功')

      // 如果启用了自动截图，等待5秒后执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        setTimeout(async () => {
          await takeScreenshot(true)
        }, autoScreenshotDelay)
      }
    } else {
      Message.error('滑动操作失败')
    }
  } catch (error) {
    Message.error('滑动操作失败: ' + error.message)
  } finally {
    swipeLoading.value = false
  }
}

// 工具方法
const convertDomTreeToTreeData = (elements) => {
  if (!elements || !Array.isArray(elements)) {
    console.error('DOM树元素数据无效:', elements)
    return []
  }

  // 将扁平的元素列表转换为树形结构
  const treeData = []
  const elementMap = new Map()

  elements.forEach((element, index) => {
    // 确保每个元素都有必要的属性
    const safeElement = {
      seq_index: element.seq_index || index,
      index: element.index || String(index),
      name: element.name || element.class_name || 'Unknown',
      text: element.text || '',
      visible: element.visible !== false,
      enabled: element.enabled !== false,
      pos: element.pos || [],
      size: element.size || [],
      attributes: element.attributes || {},
      ...element
    }

    const treeNode = {
      key: safeElement.seq_index,
      title: safeElement.name,
      seq_index: safeElement.seq_index,
      index: safeElement.index,
      text: safeElement.text,
      visible: safeElement.visible,
      children: [],
      ...safeElement
    }
    elementMap.set(safeElement.index, treeNode)
  })

  // 构建树形结构 - 使用index而不是seq_index来构建层级关系
  elements.forEach((element, index) => {
    const safeIndex = element.index || String(index)
    const node = elementMap.get(safeIndex)
    if (!node) return

    const parentIndex = getParentIndex(safeIndex)

    if (parentIndex && elementMap.has(parentIndex)) {
      elementMap.get(parentIndex).children.push(node)
    } else {
      treeData.push(node)
    }
  })

  return treeData
}

const getParentIndex = (index) => {
  // index格式如: "0", "0_0", "0_0_1"
  const parts = index.split('_')
  if (parts.length > 1) {
    return parts.slice(0, -1).join('_')
  }
  return null
}

const onScreenshotClick = (event) => {
  // 获取点击坐标
  const rect = event.target.getBoundingClientRect()
  const img = event.target

  // 计算实际图片尺寸与显示尺寸的比例
  const scaleX = img.naturalWidth / img.clientWidth
  const scaleY = img.naturalHeight / img.clientHeight

  // 计算在原始图片上的坐标
  const x = Math.round((event.clientX - rect.left) * scaleX)
  const y = Math.round((event.clientY - rect.top) * scaleY)

  // 自动填充到点击表单
  clickForm.x = x
  clickForm.y = y
  clickForm.method = 'coordinates'

  // 同时填充到输入表单
  inputForm.x = x
  inputForm.y = y
  inputForm.method = 'coordinates'

  // 尝试查找点击位置对应的DOM元素
  const clickedElement = findElementByCoordinates(x, y)
  if (clickedElement) {
    selectedElement.value = clickedElement
    Message.success(`已选中元素: ${clickedElement.seq_index} (${clickedElement.name || 'Unknown'})`)

    // 自动填充到相关表单
    clickForm.seq_index = clickedElement.seq_index
    inputForm.seq_index = clickedElement.seq_index
  } else {
    Message.info(`已获取点击坐标: (${x}, ${y})`)
  }
}

const onDomNodeSelect = (_, { node }) => {
  if (!node) return

  selectedElement.value = node
  console.log('选中的DOM节点:', node)

  // 自动填充到相关表单
  if (node.seq_index) {
    clickForm.seq_index = node.seq_index
    inputForm.seq_index = node.seq_index
    clickForm.method = 'seq_index'
    inputForm.method = 'seq_index'
  }

  // 如果有位置信息，也填充坐标
  if (node.pos && Array.isArray(node.pos) && node.pos.length >= 2) {
    clickForm.x = node.pos[0]
    clickForm.y = node.pos[1]
    inputForm.x = node.pos[0]
    inputForm.y = node.pos[1]
  }

  Message.success(`已选中元素: ${node.seq_index || node.key} (${node.title || node.name || 'Unknown'})`)
}

const searchDomTree = () => {
  // 搜索逻辑已在计算属性中实现
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.2)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const fitToContainer = () => {
  if (!currentScreenshot.value) return

  // 获取截图容器的尺寸
  const container = document.querySelector('.screenshot-container')
  if (!container) return

  // 获取图片的原始尺寸
  const img = document.querySelector('.screenshot-image')
  if (!img) return

  const containerWidth = container.clientWidth - 20 // 减去padding
  const containerHeight = container.clientHeight - 20
  const imgWidth = img.naturalWidth
  const imgHeight = img.naturalHeight

  // 计算缩放比例
  const scaleX = containerWidth / imgWidth
  const scaleY = containerHeight / imgHeight
  const scale = Math.min(scaleX, scaleY, 1) // 不超过原始大小

  zoomLevel.value = Math.max(scale, 0.1) // 最小缩放0.1
  Message.success(`已自适应缩放至 ${Math.round(zoomLevel.value * 100)}%`)
}

const fillClickForm = () => {
  if (selectedElement.value) {
    clickForm.method = 'seq_index'
    clickForm.seq_index = selectedElement.value.seq_index
    activeTab.value = 'click'
    Message.success('已填充到点击操作')
  }
}

const fillInputForm = () => {
  if (selectedElement.value) {
    inputForm.method = 'seq_index'
    inputForm.seq_index = selectedElement.value.seq_index
    activeTab.value = 'input'
    Message.success('已填充到输入操作')
  }
}

const findElementByCoordinates = (x, y) => {
  if (!domTree.value || !domTree.value.elements) {
    return null
  }

  // 查找包含点击坐标的元素
  let bestMatch = null
  let smallestArea = Infinity

  for (const element of domTree.value.elements) {
    const bounds = element.attributes?.bounds
    if (bounds && bounds.length === 4) {
      const [left, top, right, bottom] = bounds

      // 检查点击坐标是否在元素边界内
      if (x >= left && x <= right && y >= top && y <= bottom) {
        const area = (right - left) * (bottom - top)

        // 选择面积最小的元素（最精确的匹配）
        if (area < smallestArea) {
          smallestArea = area
          bestMatch = element
        }
      }
    }
  }

  return bestMatch
}

// DOM树双击展开功能
const toggleDomNodeExpand = (nodeKey) => {
  if (domTreeRef.value) {
    const expandedKeys = domTreeRef.value.expandedKeys || []
    const isExpanded = expandedKeys.includes(nodeKey)

    if (isExpanded) {
      // 折叠节点
      domTreeRef.value.expandNode(nodeKey, false)
    } else {
      // 展开节点
      domTreeRef.value.expandNode(nodeKey, true)
    }
  }
}

// 展开全部DOM节点
const expandAllDomNodes = () => {
  if (domTree.value && domTree.value.elements) {
    const allKeys = getAllDomNodeKeys(filteredDomTree.value)
    console.log('展开所有节点，keys:', allKeys)
    expandedKeys.value = allKeys
    Message.success(`已展开全部节点 (${allKeys.length}个)`)
  }
}

// 收起全部DOM节点
const collapseAllDomNodes = () => {
  console.log('收起所有节点')
  expandedKeys.value = []
  Message.success('已收起全部节点')
}

// 悬浮窗口控制
const openFloatingWindow = () => {
  if (!deviceStore.isConnected) {
    Message.error('请先连接设备')
    return
  }
  showFloatingWindow.value = true
  Message.success('悬浮流媒体窗口已打开')
}

const closeFloatingWindow = () => {
  showFloatingWindow.value = false
  Message.success('悬浮窗口已关闭')
}

const onFloatingWindowClose = () => {
  showFloatingWindow.value = false
}

const onCoordinateClick = (coordinates) => {
  // 自动填充坐标到操作表单
  if (activeTab.value === 'click') {
    clickForm.x = coordinates.x
    clickForm.y = coordinates.y
  } else if (activeTab.value === 'input') {
    inputForm.x = coordinates.x
    inputForm.y = coordinates.y
  }
  Message.success(`坐标已自动填充: (${coordinates.x}, ${coordinates.y})`)
}

// 获取所有DOM节点的key
const getAllDomNodeKeys = (treeData) => {
  const keys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      keys.push(node.key)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(treeData)
  return keys
}

// ADB调试方法
const executeAdbCommand = async () => {
  if (!adbCommand.value.trim()) return

  adbLoading.value = true
  try {
    const response = await debugApi.executeAdbCommand({ command: adbCommand.value })
    adbResult.value = response.data.result

    if (response.data.success) {
      Message.success('ADB命令执行成功')
    } else {
      Message.warning('ADB命令执行完成，但可能有错误')
    }

    // 刷新历史记录
    await refreshAdbHistory()
  } catch (error) {
    Message.error('ADB命令执行失败: ' + error.message)
  } finally {
    adbLoading.value = false
  }
}

const refreshAdbHistory = async () => {
  loadingAdbHistory.value = true
  try {
    const response = await debugApi.getAdbHistory()
    adbHistory.value = response.data || []
  } catch (error) {
    console.error('获取ADB历史记录失败:', error)
  } finally {
    loadingAdbHistory.value = false
  }
}

const selectAdbCommand = (command) => {
  adbCommand.value = command
}

const clearAdbResult = () => {
  adbResult.value = ''
}

// 设备日志相关方法
const getDeviceLogs = async () => {
  logLoading.value = true
  try {
    const response = await debugApi.getDeviceLogs()
    deviceLogs.value = response.data.data.logs
    Message.success('获取设备日志成功')
  } catch (error) {
    Message.error('获取设备日志失败: ' + error.message)
  } finally {
    logLoading.value = false
  }
}

const clearDeviceLogs = async () => {
  try {
    await debugApi.clearDeviceLogs()
    deviceLogs.value = ''
    Message.success('设备日志已清空')
  } catch (error) {
    Message.error('清空设备日志失败: ' + error.message)
  }
}

// Poco调试方法
const executePocoDebug = async () => {
  if (!pocoDebugForm.poco_statement.trim()) return

  pocoDebugLoading.value = true
  try {
    const response = await debugApi.executePocoQuery({
      query: pocoDebugForm.poco_statement
    })
    pocoDebugResult.value = response.data.result

    if (response.data.success) {
      Message.success('Poco语句执行成功')
    } else {
      Message.warning('Poco语句执行完成，但可能有错误')
    }
  } catch (error) {
    Message.error('Poco语句执行失败: ' + error.message)
    pocoDebugResult.value = `执行失败: ${error.message}`
  } finally {
    pocoDebugLoading.value = false
  }
}

// 截图管理方法
const openScreenshotManager = async () => {
  showScreenshotManager.value = true
  await refreshScreenshots()
}

const refreshScreenshots = async () => {
  loadingScreenshots.value = true
  try {
    const response = await operationApi.getScreenshots()
    screenshots.value = response.data.screenshots || []
  } catch (error) {
    Message.error('获取截图列表失败: ' + error.message)
  } finally {
    loadingScreenshots.value = false
  }
}

const deleteScreenshot = async (filename) => {
  try {
    await operationApi.deleteScreenshot(filename)
    Message.success('截图删除成功')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除截图失败: ' + error.message)
  }
}

const deleteAllScreenshots = async () => {
  try {
    await operationApi.deleteAllScreenshots()
    Message.success('所有截图删除成功')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除所有截图失败: ' + error.message)
  }
}

const viewScreenshot = (screenshot) => {
  currentViewingScreenshot.value = screenshot
  showScreenshotViewer.value = true
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理元素详情数据
const getElementDetailsData = (element) => {
  if (!element) return []

  const details = [
    { field: 'seq_index', value: element.seq_index || '无', type: 'tag', color: 'blue' },
    { field: 'index', value: element.index || '无', type: 'tag', color: 'green' },
    { field: 'name', value: element.name || element.class_name || '无' },
    { field: 'text', value: element.text || '无' },
    { field: 'class_name', value: element.class_name || '无' },
    { field: 'type', value: element.type || '无' },
    { field: 'pos', value: element.pos && element.pos.length >= 2 ? `(${element.pos[0]}, ${element.pos[1]})` : '无', type: 'code' },
    { field: 'size', value: element.size && element.size.length >= 2 ? `${element.size[0]} × ${element.size[1]}` : '无', type: 'code' },
    { field: 'visible', value: element.visible ? '可见' : '隐藏', type: 'tag', color: element.visible ? 'green' : 'red' },
    { field: 'enabled', value: element.enabled !== false ? '可用' : '禁用', type: 'tag', color: element.enabled !== false ? 'green' : 'red' },
    { field: 'clickable', value: element.clickable ? '可点击' : '不可点击', type: 'tag', color: element.clickable ? 'green' : 'orange' },
    { field: 'focusable', value: element.focusable ? '可聚焦' : '不可聚焦', type: 'tag', color: element.focusable ? 'green' : 'orange' },
    { field: 'scrollable', value: element.scrollable ? '可滚动' : '不可滚动', type: 'tag', color: element.scrollable ? 'green' : 'orange' }
  ]

  // 添加attributes中的信息
  if (element.attributes) {
    if (element.attributes.class) {
      details.push({ field: 'class', value: element.attributes.class, type: 'code' })
    }
    if (element.attributes.resource_id) {
      details.push({ field: 'resource_id', value: element.attributes.resource_id, type: 'code' })
    }
    if (element.attributes.package) {
      details.push({ field: 'package', value: element.attributes.package })
    }
    if (element.attributes.bounds && Array.isArray(element.attributes.bounds)) {
      details.push({ field: 'bounds', value: `[${element.attributes.bounds.join(', ')}]`, type: 'code' })
    }
    if (element.attributes.content_desc) {
      details.push({ field: 'content_desc', value: element.attributes.content_desc })
    }
  }

  return details.filter(item => item.value !== '无' && item.value !== null && item.value !== undefined)
}

// 生命周期
onMounted(async () => {
  await refreshAdbHistory()
})
</script>

<style scoped>
.operation-page {
  padding: 16px;
  overflow-x: hidden;
}

.page-header {
  margin-bottom: 12px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-1);
}

.first-row,
.second-row,
.third-row {
  margin-bottom: 16px;
}

/* 固定卡片高度 */
.screenshot-card,
.dom-tree-card {
  height: 500px;
}

.element-details-card,
.operation-card {
  height: 450px;
}

.adb-debug-card,
.device-logs-card {
  height: 400px;
}

.screenshot-card :deep(.arco-card-body),
.dom-tree-card :deep(.arco-card-body),
.element-details-card :deep(.arco-card-body),
.operation-card :deep(.arco-card-body),
.adb-debug-card :deep(.arco-card-body),
.device-logs-card :deep(.arco-card-body) {
  height: calc(100% - 57px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.streaming-section {
  height: 500px;
  overflow: hidden;
}

.streaming-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.streaming-actions {
  display: flex;
  gap: 12px;
}

.streaming-tips {
  margin-top: 20px;
  max-width: 500px;
}

.screen-mode-tabs {
  display: flex;
  align-items: center;
}

.screen-mode-tabs .arco-radio-group {
  margin: 0;
}

.screen-mode-tabs .arco-radio-button {
  border-radius: 4px;
}

.screen-mode-tabs .arco-radio-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.screen-mode-tabs .arco-radio-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.screenshot-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.screenshot-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.screenshot-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.screenshot-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  width: 100%;
  overflow: auto;
  text-align: center;
  background: #f5f5f5;
  position: relative;
  flex: 1;
  height: 380px;
}

.screenshot-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 10px;
}

.screenshot-image {
  max-width: none;
  cursor: crosshair;
  transition: transform 0.2s;
  object-fit: contain;
  display: block;
}

.no-screenshot,
.no-dom-tree {
  padding: 20px 0;
  text-align: center;
}

.dom-tree-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dom-tree-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dom-tree-header {
  margin-bottom: 8px;
  flex-shrink: 0;
}

.dom-tree-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 8px;
  overflow: auto;
  flex: 1;
  height: 400px;
}

.dom-node-title {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  user-select: none;
}

.dom-node-title:hover {
  background-color: var(--color-fill-1);
  border-radius: 4px;
}

.seq-index {
  background: var(--color-primary-light-1);
  color: var(--color-primary);
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 11px;
  font-family: monospace;
}

.node-name {
  font-weight: 500;
  font-size: 12px;
}

.node-text {
  color: var(--color-text-3);
  font-size: 11px;
}

.operation-card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.auto-screenshot-setting {
  display: flex;
  align-items: center;
  gap: 6px;
}

.setting-label {
  font-size: 11px;
  color: var(--color-text-2);
}

.operation-card :deep(.arco-tabs-content) {
  padding-top: 12px;
}

.operation-card :deep(.arco-form-item) {
  margin-bottom: 8px;
}

.operation-card :deep(.arco-form-item-label) {
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.2;
}

.operation-card :deep(.arco-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.operation-card :deep(.arco-radio) {
  margin-right: 0;
  font-size: 13px;
}

.operation-card :deep(.arco-input-number),
.operation-card :deep(.arco-input),
.operation-card :deep(.arco-textarea),
.operation-card :deep(.arco-select) {
  font-size: 13px;
}

.operation-card :deep(.arco-input-number) {
  width: 100%;
}

.operation-card :deep(.arco-btn) {
  font-size: 13px;
  height: 32px;
  padding: 0 12px;
}

.poco-help-text {
  font-size: 12px;
  color: var(--color-text-3);
  margin-bottom: 8px;
  padding: 4px 8px;
  background: var(--color-fill-1);
  border-radius: 4px;
  border-left: 3px solid var(--color-primary-light-4);
}

.poco-debug-result {
  margin-top: 12px;
}

.result-textarea {
  font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
}

/* 元素详情样式 */
.element-details-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.element-details-content :deep(.arco-table) {
  flex: 1;
}

.element-details-content :deep(.arco-table-container) {
  height: 100%;
}

.element-details-content :deep(.arco-table-body) {
  overflow-y: auto;
}

.element-details-content :deep(.arco-table-td) {
  padding: 4px 8px;
  font-size: 12px;
}

.element-details-content :deep(.arco-table-th) {
  padding: 6px 8px;
  font-size: 12px;
  font-weight: 600;
}

.element-actions-header {
  display: flex;
  align-items: center;
}

.dom-tree-actions {
  display: flex;
  align-items: center;
}

.element-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--color-border);
  flex-shrink: 0;
}

.no-element-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 操作面板样式 */
.operation-content {
  flex: 1;
  overflow: hidden;
}

.operation-content :deep(.arco-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.operation-content :deep(.arco-tabs-content) {
  flex: 1;
  overflow: auto;
  padding: 8px 12px;
}

.operation-content :deep(.arco-tabs-tab) {
  font-size: 11px;
  padding: 4px 8px;
}

.operation-content :deep(.arco-form) {
  height: 100%;
}

.operation-content :deep(.arco-row) {
  margin-bottom: 0;
}

/* ADB调试样式 */
.adb-debug-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.command-input-section {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.command-result-section {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.command-history-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.result-header,
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.result-header h4,
.history-header h4 {
  margin: 0;
  font-size: 14px;
}

.result-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-fill-1);
  overflow: auto;
  height: 100px;
}

.result-text {
  padding: 8px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.history-container {
  flex: 1;
  overflow: auto;
}

.history-item {
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: var(--color-fill-1);
}

.history-content {
  width: 100%;
}

.history-command {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

.history-command span {
  margin-left: 6px;
}

.history-time {
  font-size: 10px;
  color: var(--color-text-3);
}

/* 设备日志样式 */
.log-actions {
  display: flex;
  gap: 8px;
}

.device-logs-content {
  flex: 1;
  overflow: hidden;
}

.logs-textarea {
  font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  height: 100%;
}

.logs-textarea :deep(.arco-textarea) {
  height: 320px !important;
  resize: none;
}

/* 截图管理样式 */
.screenshot-manager {
  overflow-y: auto;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border);
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.screenshot-item {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.screenshot-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.screenshot-preview {
  min-height: 100px;
  overflow: hidden;
  background: var(--color-fill-1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-preview img {
  max-width: 100%;
  object-fit: contain;
  cursor: pointer;
}

.screenshot-info {
  padding: 6px 8px;
  background: var(--color-bg-2);
}

.screenshot-info .filename {
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 2px;
  word-break: break-all;
}

.screenshot-info .time {
  font-size: 10px;
  color: var(--color-text-3);
  margin-bottom: 1px;
}

.screenshot-info .size {
  font-size: 10px;
  color: var(--color-text-3);
}

.screenshot-actions {
  padding: 6px 8px;
  display: flex;
  gap: 6px;
  border-top: 1px solid var(--color-border);
}

.screenshot-viewer {
  text-align: center;
  overflow: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .operation-page {
    padding: 15px;
  }

  .main-row :deep(.arco-col),
  .second-row :deep(.arco-col) {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .operation-page {
    padding: 10px;
  }

  .operation-buttons {
    flex-direction: column;
  }

  .operation-buttons .arco-btn {
    width: 100%;
  }

  .screenshot-container {
    min-height: 150px;
    max-height: 250px;
  }

  .dom-tree-container {
    max-height: 200px;
  }

  .screenshots-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>
