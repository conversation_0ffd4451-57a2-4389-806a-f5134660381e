<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流媒体测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        canvas {
            border: 1px solid #ddd;
            max-width: 100%;
            height: auto;
        }
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DebugTools 流媒体测试页面</h1>
        
        <!-- 设备连接测试 -->
        <div class="section">
            <h3>1. 设备连接测试</h3>
            <input type="text" id="deviceId" placeholder="设备ID (如: 3ef2ce8b)" value="3ef2ce8b">
            <button onclick="testDeviceConnection()">测试设备连接</button>
            <button onclick="testScreenshot()">测试截图</button>
            <div id="deviceStatus" class="status info">等待测试...</div>
        </div>

        <!-- WebSocket连接测试 -->
        <div class="section">
            <h3>2. WebSocket连接测试</h3>
            <button onclick="connectWebSocket()">连接WebSocket</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <div id="wsStatus" class="status info">未连接</div>
        </div>

        <!-- 流媒体测试 -->
        <div class="section">
            <h3>3. 流媒体测试</h3>
            <button onclick="startStreaming()">开始流媒体</button>
            <button onclick="stopStreaming()">停止流媒体</button>
            <div id="streamStatus" class="status info">未开始</div>
            
            <div class="canvas-container">
                <canvas id="streamCanvas" width="400" height="600"></canvas>
            </div>
        </div>

        <!-- 日志输出 -->
        <div class="section">
            <h3>4. 调试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="logOutput" class="log">等待操作...</div>
        </div>
    </div>

    <script>
        let ws = null;
        let isStreaming = false;
        let frameCount = 0;

        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('logOutput').textContent = '';
        }

        async function testDeviceConnection() {
            const deviceId = document.getElementById('deviceId').value;
            if (!deviceId) {
                setStatus('deviceStatus', '请输入设备ID', 'error');
                return;
            }

            log(`测试设备连接: ${deviceId}`);
            try {
                const response = await fetch('/api/android/device/info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ device_id: deviceId })
                });
                
                const data = await response.json();
                if (data.success) {
                    setStatus('deviceStatus', '设备连接正常', 'success');
                    log('设备连接测试成功');
                } else {
                    setStatus('deviceStatus', '设备连接失败', 'error');
                    log(`设备连接失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('deviceStatus', '连接测试出错', 'error');
                log(`连接测试出错: ${error.message}`);
            }
        }

        async function testScreenshot() {
            const deviceId = document.getElementById('deviceId').value;
            if (!deviceId) {
                setStatus('deviceStatus', '请输入设备ID', 'error');
                return;
            }

            log(`测试截图: ${deviceId}`);
            try {
                const response = await fetch(`/api/streaming/test-screenshot/${deviceId}`);
                const data = await response.json();
                
                if (data.success) {
                    setStatus('deviceStatus', '截图测试成功', 'success');
                    log(`截图测试成功，图片大小: ${data.data.image_size} 字节`);
                } else {
                    setStatus('deviceStatus', '截图测试失败', 'error');
                    log(`截图测试失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('deviceStatus', '截图测试出错', 'error');
                log(`截图测试出错: ${error.message}`);
            }
        }

        function connectWebSocket() {
            if (ws) {
                log('WebSocket已连接');
                return;
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/api/streaming/stream`;
            
            log(`连接WebSocket: ${wsUrl}`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                setStatus('wsStatus', 'WebSocket已连接', 'success');
                log('WebSocket连接成功');
            };
            
            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    log(`收到消息: ${message.type}`);
                    
                    if (message.type === 'frame') {
                        drawFrame(message);
                        frameCount++;
                        if (frameCount % 10 === 0) {
                            log(`已接收 ${frameCount} 帧`);
                        }
                    }
                } catch (error) {
                    log(`解析消息失败: ${error.message}`);
                }
            };
            
            ws.onclose = () => {
                setStatus('wsStatus', 'WebSocket已断开', 'error');
                log('WebSocket连接关闭');
                ws = null;
            };
            
            ws.onerror = (error) => {
                setStatus('wsStatus', 'WebSocket连接错误', 'error');
                log(`WebSocket错误: ${error}`);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                setStatus('wsStatus', 'WebSocket已断开', 'info');
                log('手动断开WebSocket连接');
            }
        }

        function drawFrame(frameData) {
            const canvas = document.getElementById('streamCanvas');
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            img.onload = () => {
                // 计算适合的尺寸
                const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
                const width = img.width * scale;
                const height = img.height * scale;
                
                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 居中绘制
                const x = (canvas.width - width) / 2;
                const y = (canvas.height - height) / 2;
                
                ctx.drawImage(img, x, y, width, height);
            };
            
            img.src = `data:image/png;base64,${frameData.data}`;
        }

        async function startStreaming() {
            const deviceId = document.getElementById('deviceId').value;
            if (!deviceId) {
                setStatus('streamStatus', '请输入设备ID', 'error');
                return;
            }

            if (!ws) {
                setStatus('streamStatus', '请先连接WebSocket', 'error');
                return;
            }

            log(`开始流媒体: ${deviceId}`);
            try {
                const response = await fetch('/api/streaming/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        device_id: deviceId,
                        fps: 10,
                        quality: 80,
                        scale: 1.0
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    setStatus('streamStatus', '流媒体已开始', 'success');
                    log('流媒体启动成功');
                    isStreaming = true;
                    frameCount = 0;
                } else {
                    setStatus('streamStatus', '流媒体启动失败', 'error');
                    log(`流媒体启动失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('streamStatus', '流媒体启动出错', 'error');
                log(`流媒体启动出错: ${error.message}`);
            }
        }

        async function stopStreaming() {
            log('停止流媒体');
            try {
                const response = await fetch('/api/streaming/stop', {
                    method: 'POST'
                });
                
                const data = await response.json();
                if (data.success) {
                    setStatus('streamStatus', '流媒体已停止', 'info');
                    log('流媒体停止成功');
                    isStreaming = false;
                } else {
                    setStatus('streamStatus', '流媒体停止失败', 'error');
                    log(`流媒体停止失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('streamStatus', '流媒体停止出错', 'error');
                log(`流媒体停止出错: ${error.message}`);
            }
        }

        // 页面加载完成后的初始化
        window.onload = () => {
            log('流媒体测试页面已加载');
            log('请按顺序进行测试：');
            log('1. 输入设备ID并测试设备连接');
            log('2. 连接WebSocket');
            log('3. 开始流媒体测试');
        };
    </script>
</body>
</html>
